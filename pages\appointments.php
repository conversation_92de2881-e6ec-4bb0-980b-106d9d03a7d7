<?php
    // Include authentication check
    require_once __DIR__ . '/../includes/auth_check.php';

    // Include the appointment functions
    require_once __DIR__ . '/../includes/appointment_functions.php';

    // Set page title and active page
    $pageTitle  = 'Appointments';
    $activePage = 'appointments';

    // Include header
    include __DIR__ . '/../includes/header.php';

    // Set a default user ID for demonstration
    $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 4; // This would normally come from the logged-in user's session

    // Get user's appointments
    $appointments = getUserAppointments($userId);

    // Get all doctors and locations
    $doctors   = getAllDoctors();
    $locations = getAllLocations();

    // Initialize variables
    $editAppointment = null;
    $message         = '';
    $messageType     = '';

    // Process form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action'])) {
            // Create new appointment
            if ($_POST['action'] === 'create') {
                $doctorId   = filter_input(INPUT_POST, 'doctor_id', FILTER_VALIDATE_INT);
                $locationId = filter_input(INPUT_POST, 'location_id', FILTER_VALIDATE_INT);
                $title      = filter_input(INPUT_POST, 'title', FILTER_SANITIZE_SPECIAL_CHARS);
                $date       = filter_input(INPUT_POST, 'date', FILTER_SANITIZE_SPECIAL_CHARS);
                $time       = filter_input(INPUT_POST, 'time', FILTER_SANITIZE_SPECIAL_CHARS);
                $duration   = filter_input(INPUT_POST, 'duration', FILTER_VALIDATE_INT);
                $notes      = filter_input(INPUT_POST, 'notes', FILTER_SANITIZE_SPECIAL_CHARS);

                if ($date && $time && $title) {
                    $result = createAppointmentWithAgenda($userId, $doctorId, $locationId, $title, $date, $time, $duration, $notes);

                    if ($result) {
                        $message     = "Appointment created successfully!";
                        $messageType = "success";

                        // Refresh appointments list
                        $appointments = getUserAppointments($userId);
                    } else {
                        $message     = "Error creating appointment.";
                        $messageType = "error";
                    }
                } else {
                    $message     = "Please fill in all required fields.";
                    $messageType = "error";
                }
            }

            // Update appointment
            if ($_POST['action'] === 'update' && isset($_POST['appointment_id'])) {
                $appointmentId = filter_input(INPUT_POST, 'appointment_id', FILTER_VALIDATE_INT);
                $doctorId      = filter_input(INPUT_POST, 'doctor_id', FILTER_VALIDATE_INT);
                $locationId    = filter_input(INPUT_POST, 'location_id', FILTER_VALIDATE_INT);
                $date          = filter_input(INPUT_POST, 'date', FILTER_SANITIZE_SPECIAL_CHARS);
                $time          = filter_input(INPUT_POST, 'time', FILTER_SANITIZE_SPECIAL_CHARS);
                $notes         = filter_input(INPUT_POST, 'notes', FILTER_SANITIZE_SPECIAL_CHARS);

                if ($appointmentId && $date && $time) {
                    $result = updateAppointment($appointmentId, $doctorId, $locationId, $date, $time, $notes);

                    if ($result) {
                        $message     = "Appointment updated successfully!";
                        $messageType = "success";

                        // Refresh appointments list
                        $appointments = getUserAppointments($userId);
                    } else {
                        $message     = "Error updating appointment.";
                        $messageType = "error";
                    }
                } else {
                    $message     = "Please fill in all required fields.";
                    $messageType = "error";
                }
            }

            // Delete appointment
            if ($_POST['action'] === 'delete' && isset($_POST['appointment_id'])) {
                $appointmentId = filter_input(INPUT_POST, 'appointment_id', FILTER_VALIDATE_INT);

                if ($appointmentId && deleteAppointment($appointmentId)) {
                    $message     = "Appointment deleted successfully!";
                    $messageType = "success";

                    // Refresh appointments list
                    $appointments = getUserAppointments($userId);
                } else {
                    $message     = "Error deleting appointment.";
                    $messageType = "error";
                }
            }
        }
    }

    // Check if we're editing an appointment
    if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['id'])) {
        $appointmentId = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
        if ($appointmentId) {
            $editAppointment = getAppointmentById($appointmentId);
        }
    }
?>

<?php
    // Additional CSS for appointments page
    $additionalHeadContent = <<<EOT
<style>
    // Remove all the CSS here
</style>
EOT;
?>

<main>
    <div class="appointments-container">
        <?php if (! empty($message)): ?>
            <div class="message<?php echo $messageType; ?>">
                <?php echo $message; ?>
            </div>
        <?php endif; ?>

        <?php if ($isLoggedIn): ?>
            <section class="content-section">
                <div class="content-wrapper">
                    <div class="content-box">
                        <h2><?php echo $editAppointment ? 'Edit Appointment' : 'Schedule New Appointment'; ?></h2>
                        <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                            <input type="hidden" name="action" value="<?php echo $editAppointment ? 'update' : 'create'; ?>">
                            <?php if ($editAppointment): ?>
                                <input type="hidden" name="appointment_id" value="<?php echo $editAppointment['id']; ?>">
                            <?php endif; ?>

                            <?php if (! $editAppointment): ?>
                                <div class="form-group">
                                    <label for="title">Appointment Title:</label>
                                    <input type="text" id="title" name="title" required
                                        placeholder="e.g., Prenatal Checkup, Ultrasound, etc.">
                                </div>
                            <?php endif; ?>

                            <div class="form-group">
                                <label for="doctor_id">Doctor:</label>
                                <select id="doctor_id" name="doctor_id">
                                    <option value="">Select a Doctor</option>
                                    <?php foreach ($doctors as $doctor): ?>
                                        <option value="<?php echo $doctor['id']; ?>"
                                            <?php echo($editAppointment && $editAppointment['doctor_id'] == $doctor['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($doctor['name']); ?>
<?php echo ! empty($doctor['specialty']) ? ' (' . htmlspecialchars($doctor['specialty']) . ')' : ''; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                               <!--  <a href="manage_doctors_locations.php" class="small-link">Manage Doctors</a> -->
                            </div>

                            <div class="form-group">
                                <label for="location_id">Location:</label>
                                <select id="location_id" name="location_id">
                                    <option value="">Select a Location</option>
                                    <?php foreach ($locations as $location): ?>
                                        <option value="<?php echo $location['id']; ?>"
                                            <?php echo($editAppointment && $editAppointment['location_id'] == $location['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($location['name']); ?>
<?php echo ! empty($location['address']) ? ' (' . htmlspecialchars($location['address']) . ')' : ''; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                               <!--  <a href="manage_doctors_locations.php?tab=locations" class="small-link">Manage Locations</a> -->
                            </div>

                            <div class="form-group">
                                <label for="date">Date:</label>
                                <input type="date" id="date" name="date" required
                                    value="<?php echo $editAppointment ? htmlspecialchars($editAppointment['date']) : ''; ?>">
                            </div>

                            <div class="form-group">
                                <label for="time">Time:</label>
                                <input type="time" id="time" name="time" required
                                    value="<?php echo $editAppointment ? htmlspecialchars($editAppointment['time']) : ''; ?>">
                            </div>

                            <?php if (! $editAppointment): ?>
                                <div class="form-group">
                                    <label for="duration">Duration (minutes):</label>
                                    <select id="duration" name="duration">
                                        <option value="30">30 minutes</option>
                                        <option value="45">45 minutes</option>
                                        <option value="60" selected>1 hour</option>
                                        <option value="90">1.5 hours</option>
                                        <option value="120">2 hours</option>
                                    </select>
                                </div>
                            <?php endif; ?>

                            <div class="form-group">
                                <label for="notes">Notes:</label>
                                <textarea id="notes" name="notes" rows="3" placeholder="Any special instructions or things to remember..."><?php echo $editAppointment ? htmlspecialchars($editAppointment['notes']) : ''; ?></textarea>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="submit-btn">
                                    <?php echo $editAppointment ? 'Update Appointment' : 'Schedule Appointment'; ?>
                                </button>
                                <?php if ($editAppointment): ?>
                                    <a href="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" class="cancel-btn">Cancel</a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>

                    <div class="content-box">
                        <h2>Your Appointments</h2>

                        <?php if (empty($appointments)): ?>
                            <p>You don't have any appointments scheduled.</p>
                        <?php else: ?>
<?php
    // Group appointments by month
    $groupedAppointments = [];
    $monthNames          = [
        1  => 'January',
        2  => 'February',
        3  => 'March',
        4  => 'April',
        5  => 'May',
        6  => 'June',
        7  => 'July',
        8  => 'August',
        9  => 'September',
        10 => 'October',
        11 => 'November',
        12 => 'December',
    ];

    foreach ($appointments as $appointment) {
        $monthNum = date('n', strtotime($appointment['date']));
        $year     = date('Y', strtotime($appointment['date']));
        $month    = $monthNames[$monthNum] . ' ' . $year;
        if (! isset($groupedAppointments[$month])) {
            $groupedAppointments[$month] = [];
        }
        $groupedAppointments[$month][] = $appointment;
    }

    // Display appointments by month
    foreach ($groupedAppointments as $month => $monthAppointments):
?>
                                <h3 class="month-heading"><?php echo $month; ?></h3>

                                <?php foreach ($monthAppointments as $appointment): ?>
                                    <div class="appointment-card">
                                        <div class="appointment-date">
                                            <i class="far fa-calendar"></i>
                                            <?php
                                                // Format date with English (UK) day and month names
                                                $timestamp  = strtotime($appointment['date']);
                                                $day        = date('j', $timestamp);
                                                $monthNum   = date('n', $timestamp);
                                                $year       = date('Y', $timestamp);
                                                $dayOfWeek  = date('l', $timestamp);
                                                $monthNames = [
                                                    1  => 'January',
                                                    2  => 'February',
                                                    3  => 'March',
                                                    4  => 'April',
                                                    5  => 'May',
                                                    6  => 'June',
                                                    7  => 'July',
                                                    8  => 'August',
                                                    9  => 'September',
                                                    10 => 'October',
                                                    11 => 'November',
                                                    12 => 'December',
                                                ];
                                            echo $dayOfWeek . ', ' . $day . ' ' . $monthNames[$monthNum] . ' ' . $year;
                                            ?> at
                                            <?php echo date('g:i A', strtotime($appointment['time'])); ?>
                                        </div>

                                        <?php if (! empty($appointment['doctor_name'])): ?>
                                            <p><strong>Doctor:</strong><?php echo htmlspecialchars($appointment['doctor_name']); ?>
<?php if (! empty($appointment['doctor_specialty'])): ?>
                                                    (<?php echo htmlspecialchars($appointment['doctor_specialty']); ?>)
                                                <?php endif; ?>
                                            </p>
                                        <?php endif; ?>

                                        <?php if (! empty($appointment['location_name'])): ?>
                                            <p><strong>Location:</strong><?php echo htmlspecialchars($appointment['location_name']); ?>
<?php if (! empty($appointment['location_address'])): ?>
                                                    -<?php echo htmlspecialchars($appointment['location_address']); ?>
<?php endif; ?>
                                            </p>
                                        <?php endif; ?>

                                        <?php if (! empty($appointment['notes'])): ?>
                                            <p><strong>Notes:</strong><?php echo htmlspecialchars($appointment['notes']); ?></p>
                                        <?php endif; ?>

                                        <div class="appointment-actions">
                                            <a href="?action=edit&id=<?php echo $appointment['id']; ?>" class="edit-btn">Edit</a>

                                            <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>"
                                                onsubmit="return confirm('Are you sure you want to delete this appointment?');"
                                                style="display: inline;">
                                                <input type="hidden" name="action" value="delete">
                                                <input type="hidden" name="appointment_id" value="<?php echo $appointment['id']; ?>">
                                                <button type="submit" class="delete-btn">Delete</button>
                                            </form>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
<?php endforeach; ?>
<?php endif; ?>
                    </div>
                </div>
            </section>
        <?php else: ?>
            <div class="login-prompt">
                <h2>Access Your Appointments</h2>
                <p>Please log in to view and manage your appointments.</p>
                <a href="#" class="login-trigger login-prompt-btn">Login</a>
            </div>
        <?php endif; ?>
    </div>
</main>

<?php include __DIR__ . '/../includes/footer.php'; ?>
</div>

<script src="../assets/js/script.js"></script>



