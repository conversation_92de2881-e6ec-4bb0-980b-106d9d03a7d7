<?php

/**
 * Functions for user authentication and management
 */

// Include database connection
require_once 'database.php';

/**
 * Authenticate a user with username/email and password
 *
 * @param string $login_id User username or email
 * @param string $password User password
 * @return array|false User data if authentication successful, false otherwise
 */
function authenticateUser($login_id, $password)
{
    global $connection;

    // Check if login_id is an email or username
    $is_email = filter_var($login_id, FILTER_VALIDATE_EMAIL);

    // Prepare statement to prevent SQL injection
    // Always select the same columns for consistency
    if ($is_email) {
        $sql = "SELECT id, name, username, email, password, role FROM Users WHERE email = ?";
    } else {
        $sql = "SELECT id, name, username, email, password, role FROM Users WHERE username = ?";
    }

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("s", $login_id);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        $user = $result->fetch_assoc();

        // Verify password
        // Check if the password is hashed (starts with $2y$)
        if (substr($user['password'], 0, 4) === '$2y$') {
            // Password is hashed, use password_verify
            $passwordVerified = password_verify($password, $user['password']);
        } else {
            // Password is not hashed (legacy), compare directly
            $passwordVerified = ($password === $user['password']);

            // Upgrade to hashed password if it matches
            if ($passwordVerified) {
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                $updateSql      = "UPDATE Users SET password = ? WHERE id = ?";
                $updateStmt     = $connection->prepare($updateSql);
                $updateStmt->bind_param("si", $hashedPassword, $user['id']);
                $updateStmt->execute();
            }
        }

        if ($passwordVerified) {
            // Only set default role if it's NULL, not if it's already set
            if ($user['role'] === null) {
                // Set default role if not set
                $user['role'] = 'user';

                // Update user in database only if role is NULL
                $updateSql  = "UPDATE Users SET role = 'user' WHERE id = ? AND role IS NULL";
                $updateStmt = $connection->prepare($updateSql);
                $updateStmt->bind_param("i", $user['id']);
                $updateStmt->execute();
            }

            // Remove password from the returned data for security
            unset($user['password']);
            return $user;
        }
    }

    return false;
}

/**
 * Register a new user
 *
 * @param string $name User name
 * @param string $username User username
 * @param string $email User email
 * @param string $password User password
 * @return int|false New user ID if registration successful, false otherwise
 */
function registerUser($name, $username, $email, $password)
{
    global $connection;

    try {
        // Check if email already exists
        $checkEmailSql  = "SELECT id FROM Users WHERE email = ?";
        $checkEmailStmt = $connection->prepare($checkEmailSql);
        if (! $checkEmailStmt) {
            error_log("Prepare failed (check email): (" . $connection->errno . ") " . $connection->error);
            return false;
        }

        $checkEmailStmt->bind_param("s", $email);
        $checkEmailStmt->execute();
        $checkEmailResult = $checkEmailStmt->get_result();

        if ($checkEmailResult && $checkEmailResult->num_rows > 0) {
            // Email already exists
            return false;
        }

        // Check if username already exists
        $checkUsernameSql  = "SELECT id FROM Users WHERE username = ?";
        $checkUsernameStmt = $connection->prepare($checkUsernameSql);
        if (! $checkUsernameStmt) {
            error_log("Prepare failed (check username): (" . $connection->errno . ") " . $connection->error);
            return false;
        }

        $checkUsernameStmt->bind_param("s", $username);
        $checkUsernameStmt->execute();
        $checkUsernameResult = $checkUsernameStmt->get_result();

        if ($checkUsernameResult && $checkUsernameResult->num_rows > 0) {
            // Username already exists
            return false;
        }

        // Hash the password for security
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

        // Use a simpler approach - just insert without the role column
        // This avoids issues with parameter binding
        $sql = "INSERT INTO Users (name, username, email, password) VALUES (?, ?, ?, ?)";

        $stmt = $connection->prepare($sql);
        if (! $stmt) {
            error_log("Prepare failed: (" . $connection->errno . ") " . $connection->error);
            return false;
        }

        // Simple parameter binding - always 4 parameters
        $stmt->bind_param("ssss", $name, $username, $email, $hashedPassword);

        if ($stmt->execute()) {
            return $connection->insert_id;
        } else {
            error_log("Execute failed: (" . $stmt->errno . ") " . $stmt->error);
            return false;
        }
    } catch (Exception $e) {
        error_log("Exception in registerUser: " . $e->getMessage());
        return false;
    }

    return false;
}

/**
 * Get user by ID
 *
 * @param int $userId User ID
 * @return array|null User data or null if not found
 */
function getUserById($userId)
{
    global $connection;

    $sql  = "SELECT id, name, username, email, created_at, role FROM Users WHERE id = ?";
    $stmt = $connection->prepare($sql);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return null;
}

/**
 * Update user information
 *
 * @param int $userId User ID
 * @param string $name User name
 * @param string $username User username
 * @param string $email User email
 * @return bool True if successful, false otherwise
 */
function updateUser($userId, $name, $username, $email)
{
    global $connection;

    // Check if username already exists and is not the current user's username
    $checkSql  = "SELECT id FROM Users WHERE username = ? AND id != ?";
    $checkStmt = $connection->prepare($checkSql);
    $checkStmt->bind_param("si", $username, $userId);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();

    if ($checkResult && $checkResult->num_rows > 0) {
        // Username already exists for another user
        return false;
    }

    $sql  = "UPDATE Users SET name = ?, username = ?, email = ? WHERE id = ?";
    $stmt = $connection->prepare($sql);
    $stmt->bind_param("sssi", $name, $username, $email, $userId);

    return $stmt->execute();
}

/**
 * Change user password
 *
 * @param int $userId User ID
 * @param string $currentPassword Current password
 * @param string $newPassword New password
 * @return bool True if successful, false otherwise
 */
function changePassword($userId, $currentPassword, $newPassword)
{
    global $connection;

    // Get current user data
    $sql  = "SELECT password FROM Users WHERE id = ?";
    $stmt = $connection->prepare($sql);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        $user = $result->fetch_assoc();

        // Verify current password
        $passwordVerified = false;

        // Check if the password is hashed (starts with $2y$)
        if (substr($user['password'], 0, 4) === '$2y$') {
            // Password is hashed, use password_verify
            $passwordVerified = password_verify($currentPassword, $user['password']);
        } else {
            // Password is not hashed (legacy), compare directly
            $passwordVerified = ($currentPassword === $user['password']);
        }

        if ($passwordVerified) {
            // Hash the new password
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

            $updateSql  = "UPDATE Users SET password = ? WHERE id = ?";
            $updateStmt = $connection->prepare($updateSql);
            $updateStmt->bind_param("si", $hashedPassword, $userId);

            return $updateStmt->execute();
        }
    }

    return false;
}

/**
 * Store a remember me token in the database
 *
 * @param int $userId User ID
 * @param string $token Remember token
 * @param string $expiry Expiry date (Y-m-d H:i:s format)
 * @return bool True if successful, false otherwise
 */
function storeRememberToken($userId, $token, $expiry)
{
    global $connection;

    // Check if the RememberTokens table exists
    $checkTableSql = "SHOW TABLES LIKE 'RememberTokens'";
    $tableResult   = $connection->query($checkTableSql);

    if (! $tableResult || $tableResult->num_rows === 0) {
        // Create the table if it doesn't exist
        $createTableSql = "CREATE TABLE RememberTokens (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            token VARCHAR(255) NOT NULL,
            expiry DATETIME NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES Users(id) ON DELETE CASCADE
        )";

        if (! $connection->query($createTableSql)) {
            error_log("Failed to create RememberTokens table: " . $connection->error);
            return false;
        }
    }

    // Delete any existing tokens for this user
    $deleteSql  = "DELETE FROM RememberTokens WHERE user_id = ?";
    $deleteStmt = $connection->prepare($deleteSql);
    $deleteStmt->bind_param("i", $userId);
    $deleteStmt->execute();

    // Insert the new token
    $sql  = "INSERT INTO RememberTokens (user_id, token, expiry) VALUES (?, ?, ?)";
    $stmt = $connection->prepare($sql);

    if (! $stmt) {
        error_log("Prepare failed: (" . $connection->errno . ") " . $connection->error);
        return false;
    }

    $stmt->bind_param("iss", $userId, $token, $expiry);

    if ($stmt->execute()) {
        return true;
    } else {
        error_log("Execute failed: (" . $stmt->errno . ") " . $stmt->error);
        return false;
    }
}

/**
 * Get user by remember token
 *
 * @param string $token Remember token
 * @return array|false User data if token is valid, false otherwise
 */
function getUserByRememberToken($token)
{
    global $connection;

    // Check if the RememberTokens table exists
    $checkTableSql = "SHOW TABLES LIKE 'RememberTokens'";
    $tableResult   = $connection->query($checkTableSql);

    if (! $tableResult || $tableResult->num_rows === 0) {
        return false;
    }

    // Get the token from the database
    $sql = "SELECT u.id, u.name, u.username, u.email, u.role
            FROM RememberTokens rt
            JOIN Users u ON rt.user_id = u.id
            WHERE rt.token = ? AND rt.expiry > NOW()";

    $stmt = $connection->prepare($sql);

    if (! $stmt) {
        error_log("Prepare failed: (" . $connection->errno . ") " . $connection->error);
        return false;
    }

    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return false;
}
