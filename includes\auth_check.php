<?php
/**
 * Authentication check for protected pages
 * This file should be included at the top of any page that requires login
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
$isLoggedIn = isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;

// If not logged in, set a flag to show login prompt
$showLoginPrompt = ! $isLoggedIn;

// Determine if we're in the root directory or a subdirectory for path references
$scriptPath     = $_SERVER['SCRIPT_FILENAME'];
$isSubdirectory = (strpos($scriptPath, 'pages') !== false);
$rootPath       = $isSubdirectory ? '../' : '';

// Store the current page URL for redirection after login
$_SESSION['redirect_after_login'] = $_SERVER['REQUEST_URI'];
