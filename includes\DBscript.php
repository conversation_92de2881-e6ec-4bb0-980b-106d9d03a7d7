<!-- CREATE DATABASE IF NOT EXISTS project;

USE project;

-- Table: Users
CREATE TABLE Users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100),
    email VARCHAR(100),
    password VARCHAR(100),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Table: Pregnancy
CREATE TABLE Pregnancy (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    start_date DATE,
    due_date DATE,
    notes TEXT
);

-- Table: BabyGender
CREATE TABLE BabyGender (
    id INT AUTO_INCREMENT PRIMARY KEY,
    gender_name VARCHAR(20) UNIQUE,
    description TEXT
);

-- Table: Baby
CREATE TABLE Baby (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    name VARCHAR(100),
    gender_id INT,
    birth_date DATE,
    weight DECIMAL(5,2),
    height DECIMAL(5,2)
);

-- Table: Baby Names Directory
CREATE TABLE BabyNames (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHA<PERSON>(100),
    gender VARCHAR(10),
    origin VARCHAR(100),
    meaning TEXT
);

-- Table: Community
CREATE TABLE Community (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255),
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Table: Agenda
CREATE TABLE Agenda (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    title VARCHAR(255),
    description TEXT,
    date DATE,
    time TIME
);

-- Table: Health
CREATE TABLE Health (
    id INT AUTO_INCREMENT PRIMARY KEY,
    symptom_name VARCHAR(100) UNIQUE,
    tip TEXT
);

-- Table: Symptoms
CREATE TABLE Symptoms (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    health_id INT,
    severity VARCHAR(50),
    date_logged DATE,
    notes TEXT
);

-- Table: Doctors
CREATE TABLE Doctors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    specialty VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(100),
    notes TEXT
);

-- Table: Locations
CREATE TABLE Locations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    address VARCHAR(255),
    city VARCHAR(100),
    postal_code VARCHAR(20),
    phone VARCHAR(20),
    notes TEXT
);

-- Table: Appointments
CREATE TABLE Appointments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    doctor_id INT,
    location_id INT,
    date DATE,
    time TIME,
    notes TEXT
);

-- Add foreign keys and columns
ALTER TABLE Community
ADD COLUMN user_id INT,
ADD CONSTRAINT fk_community_user
FOREIGN KEY (user_id) REFERENCES Users(id)
ON DELETE SET NULL;

ALTER TABLE Pregnancy
ADD CONSTRAINT fk_pregnancy_user
FOREIGN KEY (user_id) REFERENCES Users(id);

ALTER TABLE Baby
ADD CONSTRAINT fk_baby_gender
FOREIGN KEY (gender_id) REFERENCES BabyGender(id)
ON DELETE RESTRICT
ON UPDATE CASCADE;

ALTER TABLE Baby
ADD COLUMN pregnancy_id INT,
ADD CONSTRAINT fk_baby_pregnancy
FOREIGN KEY (pregnancy_id) REFERENCES Pregnancy(id)
ON DELETE SET NULL;

ALTER TABLE Agenda
ADD baby_id INT;

ALTER TABLE Agenda
ADD CONSTRAINT fk_agenda_baby
FOREIGN KEY (baby_id) REFERENCES Baby(id)
ON DELETE SET NULL
ON UPDATE CASCADE;

ALTER TABLE Baby
ADD COLUMN baby_name_id INT,
ADD CONSTRAINT fk_baby_name
FOREIGN KEY (baby_name_id) REFERENCES BabyNames(id);

ALTER TABLE Appointments
ADD COLUMN agenda_id INT;

ALTER TABLE Appointments
ADD CONSTRAINT fk_appointments_agenda
FOREIGN KEY (agenda_id) REFERENCES Agenda(id)
ON DELETE CASCADE
ON UPDATE CASCADE;

ALTER TABLE Appointments
ADD CONSTRAINT fk_appointments_doctor
FOREIGN KEY (doctor_id) REFERENCES Doctors(id)
ON DELETE SET NULL
ON UPDATE CASCADE;

ALTER TABLE Appointments
ADD CONSTRAINT fk_appointments_location
FOREIGN KEY (location_id) REFERENCES Locations(id)
ON DELETE SET NULL
ON UPDATE CASCADE;

ALTER TABLE Symptoms
ADD COLUMN agenda_id INT;

ALTER TABLE Symptoms
ADD CONSTRAINT fk_symptoms_agenda
FOREIGN KEY (agenda_id) REFERENCES Agenda(id)
ON DELETE SET NULL
ON UPDATE CASCADE;

ALTER TABLE Symptoms
ADD CONSTRAINT fk_symptoms_health
FOREIGN KEY (health_id) REFERENCES Health(id)
ON DELETE RESTRICT
ON UPDATE CASCADE;

-- Insert default BabyGender values
INSERT INTO BabyGender (gender_name, description) VALUES
('Boy', 'Male gender'),
('Girl', 'Female gender'),
('Neutral', 'Neutral or unspecified gender');

-- Insert sample Doctors
INSERT INTO Doctors (name, specialty, phone, email, notes) VALUES
('Dr. Emily Clark', 'Obstetrician', '020-1234-5678', '<EMAIL>', 'Specialist in high-risk pregnancies'),
('Dr. James Smith', 'Gynaecologist', '020-2345-6789', '<EMAIL>', 'Handles routine check-ups'),
('Dr. Sarah Jones', 'Paediatrician', '020-3456-7890', '<EMAIL>', 'Expert in newborn care'),
('Dr. William Brown', 'Neonatologist', '020-4567-8901', '<EMAIL>', 'Cares for premature babies');

-- Insert sample Locations
INSERT INTO Locations (name, address, city, postal_code, phone, notes) VALUES
('St Mary\'s Hospital', 'Praed Street', 'London', 'W2 1NY', '020-3312-6666', 'University hospital with maternity services'),
('Women\'s Health Clinic', '123 Rose Street', 'Manchester', 'M1 2JQ', '0161-234-5678', 'Clinic specialising in female health'),
('Child Medical Centre', '50 Queen\'s Road', 'Birmingham', 'B1 1AA', '0121-456-7890', 'Medical centre focused on paediatrics'),
('Dr. Smith\'s Practice', '100 Oxford Street', 'London', 'W1D 1LL', '020-8765-4321', 'Private practice');

-- Insert sample Health symptoms and tips
INSERT INTO Health (symptom_name, tip) VALUES
('Morning sickness', 'Eat small, frequent meals and avoid an empty stomach. Ginger may help relieve symptoms.'),
('Fatigue', 'Rest when possible and maintain a regular sleep schedule.'),
('Back pain', 'Practice good posture and consider prenatal physiotherapy.'),
('Heartburn', 'Avoid spicy and fatty foods. Eat smaller meals more often.'),
('Swelling', 'Reduce salt intake and elevate your legs when sitting.'),
('Headache', 'Stay hydrated and rest in a dark, quiet environment.'),
('Leg cramps', 'Gently stretch muscles and ensure adequate calcium intake.'),
('Insomnia', 'Create a relaxing bedtime routine and use pillows for support.'),
('Constipation', 'Increase fibre and water intake. Gentle exercise may help.'),
('Shortness of breath', 'Maintain good posture and avoid strenuous activities.');
 -->
