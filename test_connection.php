<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

$servername = "localhost";
$username   = "root";
$password   = "Paigedaives01";
$dbname     = "project";

try {
    // Try to connect without specifying database
    $conn = new mysqli($servername, $username, $password);

    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    echo "Connected to MySQL server successfully!\n";

    // Try to select the database
    if (! $conn->select_db($dbname)) {
        die("Could not select database: " . $conn->error);
    }

    echo "Successfully selected database '$dbname'\n";

    // Test a simple query
    $result = $conn->query("SELECT 1");
    if ($result) {
        echo "Database is responding to queries\n";
    } else {
        die("Query failed: " . $conn->error);
    }

} catch (Exception $e) {
    die("Error: " . $e->getMessage());
}

$conn->close();
