/* ==========================================================================
   Base Styles
   ========================================================================== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-size: 1.1rem;
  background-color: #f2f2f2;
  color: #333;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: "Manjari", sans-serif;
  line-height: 1.6;
  letter-spacing: 0.01em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
}

/* Wrapper adjustment */
.wrapper {
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;
  padding: 0; /* Removed padding */
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Nav adjustments */
nav {
  width: 100%;
  background-color: #c2d8c2;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0.6rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.logo-container {
  position: absolute;
  left: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
}

.logo {
  font-size: 1.5rem;
  color: white;
  text-decoration: none;
  font-weight: bold;
  transition: color 0.3s ease;
}

.logo:hover {
  color: #333;
}

.nav-links {
  display: flex;
  gap: 1rem;
  margin: 0 auto;
  padding: 0;
}

.nav-links a {
  color: white;
  text-decoration: none;
  padding: 0.6rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 500;
  white-space: nowrap;
}

.nav-links a:hover {
  background-color: rgba(255, 255, 255, 0.95);
  color: #1a2e1a;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.nav-links a.active {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
}

.login-container {
  position: absolute;
  right: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
}

.login-btn {
  color: white;
  text-decoration: none;
  padding: 0.7rem 1.5rem 0.4rem 1.5rem; /* Increased top padding to 0.7rem */
  border: 2px solid white;
  border-radius: 20px;
  transition: all 0.3s ease;
  font-weight: 600; /* Added font weight */
  transform: translateY(0); /* Add explicit default position */
}

.login-btn:hover {
  background-color: rgba(255, 255, 255, 0.95);
  color: #1a2e1a; /* Very dark green, almost black */
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* User Menu Styles */
.user-menu {
  position: relative;
}

.user-menu-btn {
  color: white;
  text-decoration: none;
  padding: 0.7rem 1rem 0.4rem 1rem; /* Increased top padding to 0.7rem */
  border: 2px solid white;
  border-radius: 20px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600; /* Added font weight */
}

.user-menu-btn:hover {
  background-color: rgba(255, 255, 255, 0.95);
  color: #1a2e1a; /* Very dark green, almost black */
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.user-menu-btn i {
  font-size: 1.2rem;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 180px;
  z-index: 1000;
  margin-top: 10px;
  display: none;
  overflow: hidden;
}

.user-menu:hover .user-dropdown {
  display: block;
  animation: fadeIn 0.3s;
}

.user-dropdown a {
  display: block;
  padding: 12px 16px;
  color: #333;
  text-decoration: none;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f0f0f0;
}

.user-dropdown a:last-child {
  border-bottom: none;
}

.user-dropdown a:hover {
  background-color: rgba(255, 255, 255, 0.95);
  color: #333;
}

.user-dropdown i {
  margin-right: 8px;
  width: 16px;
  text-align: center;
}

/* Responsive user dropdown */
@media (max-width: 992px) {
  .user-dropdown {
    right: -10px;
    min-width: 160px;
  }
}

@media (max-width: 576px) {
  .user-dropdown {
    right: -20px;
    min-width: 140px;
  }

  .user-dropdown a {
    padding: 10px 12px;
    font-size: 0.9rem;
  }
}

/* ==========================================================================
   Header / Slideshow
   ========================================================================== */
.hero-section {
  position: relative;
  width: 100%;
  height: 400px; /* Reduced from 500px to 400px */
  overflow: hidden;
}

.slideshow-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.slide {
  position: absolute;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.8s ease-in-out;
  display: none;
}

.slide.active {
  opacity: 1;
  display: block;
}

.slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

/* Ensure the caption stays on top */
.slide-caption {
  position: absolute;
  bottom: 50%;
  left: 50%;
  transform: translate(-50%, 50%);
  width: 100%;
  text-align: center;
  color: white;
  z-index: 2;
  padding: 20px;
}

.slide-caption h2 {
  font-size: 3rem;
  margin-bottom: 15px;
  font-weight: bold;
  letter-spacing: 1px;
}

.slide-caption p {
  font-size: 1.5rem;
  max-width: 600px;
  margin: 0 auto;
}

/* Add a subtle gradient overlay to the slides to ensure text readability */
.slide::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2));
  z-index: 1;
}

/* Ensure caption appears above the gradient overlay */
.slide-caption {
  z-index: 2;
}

.slide-dots {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 2;
}

.dot {
  width: 12px;
  height: 12px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.dot.active {
  background-color: white;
}

/* Remove the old fade animation */
.fade {
  opacity: 1;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.fadeIn {
  animation: fadeIn 0.8s ease-in-out forwards;
}

.fadeOut {
  animation: fadeOut 0.8s ease-in-out forwards;
}

/* Navigation Arrows */
.slide-nav {
  cursor: pointer;
  position: absolute;
  top: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 24px;
  background-color: rgba(0, 0, 0, 0.2);
  transition: 0.3s ease;
  user-select: none;
  z-index: 3;
  width: 50px; /* Fixed width for the arrow areas */
  border: none; /* Remove border */
  outline: none; /* Remove outline */
}

.prev {
  left: 0;
}

.next {
  right: 0;
}

.slide-nav:hover {
  background-color: rgba(0, 0, 0, 0.4);
}

/* Remove focus outline but maintain accessibility */
.slide-nav:focus {
  outline: none;
}

/* ==========================================================================
   Main Content
   ========================================================================== */
main {
  flex: 1;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem 0; /* Reduced from 2rem to 1rem */
}

main h2 {
  font-size: 1.9rem;
  color: #6b8e23;
  margin-bottom: 1rem;
  font-weight: 600;
}

main p {
  font-size: 1.05rem;
  line-height: 1.8;
  color: #444;
  margin-bottom: 1.2rem;
}

/* Two column layout adjustments */
.two-column-layout {
  width: 100%;
  max-width: 1400px;
  margin: 2rem auto !important; /* Reduced from 4rem to 2rem */
  padding: 0; /* Removed padding */
}

.content-wrapper {
  display: flex;
  gap: 4rem;
  justify-content: center;
  flex-wrap: nowrap;
  width: 100%;
  padding: 0; /* Removed padding */
}

.content-box {
  flex: 1;
  padding: 2.5rem;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background: white;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.content-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
  background-color: rgba(255, 255, 255, 0.95);
  color: #333;
}

.content-box h2 {
  color: #2c5282;
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #b2d8b2;
  position: sticky;
  top: 0;
  background: white;
  z-index: 1;
  text-align: center; /* Keep headings centered */
}

/* FAQ Section */
.faq-section {
  min-width: 0;
  height: 600px; /* Match the width (600px) to create a cube */
  min-height: 600px; /* Force minimum height to match width */
  max-height: 600px; /* Ensure it stays cube-shaped */
  overflow-y: auto; /* Allow scrolling for content that exceeds height */
}

.faq-content,
.community-content {
  overflow-y: auto;
  flex-grow: 1;
  padding: 2.5rem;
  padding-top: 1.5rem; /* Space after the header */
  text-align: left; /* Left align content */
  display: flex;
  flex-direction: column;
  align-items: flex-start; /* Align items to the left */
}

/* Customize scrollbar */
.faq-content::-webkit-scrollbar {
  width: 8px;
}

.faq-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.faq-content::-webkit-scrollbar-thumb {
  background: #b2d8b2;
  border-radius: 4px;
}

.faq-content::-webkit-scrollbar-thumb:hover {
  background: #9ac89a;
}

.faq-item {
  width: 90%; /* Further reduced from 95% */
  margin-bottom: 1.8rem;
  margin-left: auto;
  margin-right: auto;
  padding: 1.2rem;
  border-radius: 8px;
  background-color: #f8f9fa;
  transition: background-color 0.3s ease;
  text-align: left;
}

.faq-item:hover {
  background-color: #f0f4f8;
}

.faq-item h3 {
  color: #1a365d;
  margin-bottom: 0.8rem;
  font-size: 1.25rem;
  font-weight: 600;
  text-align: left;
}

.faq-item h3::before {
  display: none;
}

.faq-item p {
  color: #4a5568;
  line-height: 1.8;
  margin-left: 0; /* Remove left margin */
  text-align: left;
  font-size: 1.05rem;
}

/* Community Section */
.community-section {
  min-width: 0;
  height: 600px; /* Match the width (600px) to create a cube */
  min-height: 600px; /* Force minimum height to match width */
  max-height: 600px; /* Ensure it stays cube-shaped */
  overflow-y: auto; /* Allow scrolling for content that exceeds height */
}

.community-content {
  overflow-y: auto;
  flex-grow: 1;
  padding: 2.5rem;
  padding-top: 1.5rem; /* Space after the header */
  text-align: left; /* Left align content */
  display: flex;
  flex-direction: column;
  align-items: flex-start; /* Align items to the left */
}

/* Customize scrollbar */
.community-content::-webkit-scrollbar {
  width: 8px;
}

.community-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.community-content::-webkit-scrollbar-thumb {
  background: #b2d8b2;
  border-radius: 4px;
}

.community-content::-webkit-scrollbar-thumb:hover {
  background: #9ac89a;
}

.post-item {
  width: 90%; /* Further reduced from 95% */
  margin-bottom: 1.8rem;
  margin-left: auto;
  margin-right: auto;
  padding: 1.2rem;
  border-radius: 8px;
  background-color: #f8f9fa;
  transition: background-color 0.3s ease;
  text-align: left;
}

.post-item:hover {
  background-color: #f0f4f8;
}

.post-item h3 {
  color: #1a365d;
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
  font-weight: 600;
  text-align: left;
}

.post-meta {
  color: #718096;
  font-size: 0.9rem;
  margin-bottom: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: flex-start; /* Left align meta information */
}

.post-meta::before {
  content: "";
  display: inline-block;
  width: 6px;
  height: 6px;
  background-color: #b2d8b2;
  border-radius: 50%;
  margin-right: 8px;
}

.post-item p {
  color: #4a5568;
  line-height: 1.6;
  text-align: left;
}

/* Common styles for main sections */
#main1,
#main2 {
  max-width: 1200px !important;
  margin: 1.5rem auto !important; /* Reduced from 3rem to 1.5rem */
  padding: 0 20px;
}

/* Update tools container to match content-wrapper */
.tools-container {
  display: flex;
  gap: 4rem;
  justify-content: center;
  flex-wrap: nowrap;
  width: 100%;
  max-width: 1100px;
  margin: 0 auto;
}

/* Style the calculator and baby names sections like content boxes */
.due-date-calculator,
.baby-names {
  flex: 0 0 600px; /* Match the width of content-box */
  min-height: 500px; /* Match the min-height of content-box */
  height: auto;
  padding: 2.5rem;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background: white;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow-y: auto;
}

/* Maintain hover effect */
.due-date-calculator:hover,
.baby-names:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
  background-color: rgba(255, 255, 255, 0.95);
  color: #333;
}

/* Center the heading */
#main2 h2 {
  text-align: center;
  margin-bottom: 3rem;
  color: #6b8e23;
  font-size: 1.8rem;
}

/* Section divider */
.section-divider {
  width: 90%;
  height: 2px;
  background-color: #b2d8b2;
  margin: 1.5rem auto !important; /* Reduced from 2.5rem to 1.5rem */
  opacity: 0.7;
}

/* Planning Pregnancy Tools Section */
#planning-tools {
  text-align: center;
  width: 100%;
  margin-top: 2.5rem !important;
  padding-bottom: 6rem !important; /* Add significant padding at the bottom */
  margin-bottom: 6rem !important; /* Increase margin at the bottom */
}

#planning-tools h2 {
  text-align: center;
  margin-bottom: 2rem; /* Reduced from 2.5rem */
  color: #6b8e23;
  font-size: 1.8rem;
}

#planning-tools .content-wrapper {
  display: flex;
  gap: 2rem; /* Reduced from 4rem */
  justify-content: center;
  flex-wrap: nowrap;
  width: 100%;
  max-width: 1000px; /* Reduced from 1100px to accommodate smaller boxes */
  margin: 0 auto;
  min-height: 600px !important;
  height: 600px !important;
}

/* Due date calculator and baby names boxes */
.due-date-calculator,
.baby-names {
  flex: 0 0 600px !important; /* Reduced from 650px */
  width: 600px !important; /* Added explicit width */
  min-height: 700px !important;
  height: 700px !important;
  max-height: 700px !important;
  padding: 2rem !important;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background: white;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow-y: auto;
  margin-bottom: 6rem !important; /* Add margin to the bottom of the boxes */
}

/* Maintain hover effect */
.due-date-calculator:hover,
.baby-names:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
  background-color: rgba(255, 255, 255, 0.95);
  color: #333;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  #planning-tools .content-wrapper {
    flex-direction: column;
    align-items: center;
    gap: 2rem;
    min-height: auto !important; /* Allow height to adjust based on content on mobile */
  }

  .due-date-calculator,
  .baby-names {
    flex: 0 0 600px !important;
    width: 600px !important;
    min-height: 700px !important;
    height: 700px !important;
    max-height: 700px !important;
    max-width: 600px !important;
  }
}

/* Maintain responsiveness */
@media (max-width: 768px) {
  #main2 h2 {
    margin-bottom: 2rem; /* Slightly less space on mobile */
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  #main1,
  #main2 {
    margin: 2rem auto !important;
  }

  .section-divider {
    margin: 1.5rem auto !important;
  }

  #planning-tools h2 {
    margin-bottom: 2rem;
  }
}

/* Calculator Form Styles */
.calculator-form {
  overflow-y: auto;
  flex-grow: 1;
  padding-right: 10px;
  margin-top: 2rem; /* Add space after the title */
}

/* Add specific spacing for the first form group */
.calculator-form .form-group:first-of-type {
  margin-bottom: 3.5rem; /* Increased from 2.5rem to create more space after the date input */
}

/* Keep the general form group spacing for other elements */
.form-group {
  margin-bottom: 2.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 1rem; /* Increased from 0.6rem */
  color: #4a5568;
  font-size: 1.05rem;
  font-weight: 500;
}

.form-group input,
.form-group select {
  width: 90% !important; /* Reduced from 100% */
  padding: 0.8rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  background-color: white;
  box-sizing: border-box; /* Ensure padding is included in width */
  color: #333;
}

.calculate-btn {
  background-color: #b2d8b2;
  color: white;
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 90% !important;
  margin: 2rem auto;
  font-size: 1.05rem;
  font-weight: 500;
}

.calculate-btn:hover {
  background-color: #9ac89a; /* Darker shade of green */
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border: none;
}

.result-box {
  width: 90% !important; /* Match other elements */
  margin: 2rem auto; /* Center like other elements */
  background-color: #f8f9fa;
  padding: 1.5rem; /* Increased from 1rem */
  border-radius: 8px;
}

.result-box p {
  margin: 0;
  text-align: left;
  font-size: 1.05rem;
  line-height: 1.8; /* Increased from 1.6 */
  color: #444;
}

/* Style for the help text */
.form-text {
  display: block;
  margin-top: 0.5rem;
  margin-bottom: 1rem;
  color: #718096;
  font-size: 0.9rem;
}

/* Baby Names Form Styles */
.baby-names-form {
  overflow-y: auto;
  flex-grow: 0; /* Changed from 1 to 0 to prevent automatic growth */
  padding-right: 10px;
  margin-bottom: 1.5rem;
  height: 40%; /* Reduced from 55% to give more space to results */
}

.baby-names-form .form-group {
  margin-bottom: 1.5rem; /* Reduced from 2rem to make form more compact */
  width: 100%;
}

.baby-names-form label {
  display: block;
  margin-bottom: 0.5rem;
  color: #4a5568;
}

.baby-names-form input,
.baby-names-form select {
  width: 90% !important;
  padding: 0.8rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.95rem;
  background-color: white;
  box-sizing: border-box;
}

.submit-name-btn,
.choose-name-btn {
  background-color: #b2d8b2;
  color: white;
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 90% !important;
  margin: 1rem auto;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-decoration: none; /* Added for anchor tags */
  text-align: center; /* Added for consistent text alignment */
}

.submit-name-btn:hover,
.choose-name-btn:hover {
  background-color: #b2d8b2;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  border: none;
  text-decoration: none; /* Ensure no underline on hover */
}

/* Keep the original hover effect for buttons in colored backgrounds (like nav buttons) */
.nav-links a:hover,
.login-btn:hover {
  background-color: rgba(255, 255, 255, 0.95);
  color: #1a2e1a;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Names List Styles */
.names-list {
  overflow-y: auto;
  flex-grow: 0; /* Changed from 1 to 0 to prevent automatic growth */
  height: 60%; /* Increased from 45% to show more results */
  padding-right: 10px;
  margin-top: 1rem;
}

.names-list h4 {
  margin-bottom: 1rem;
  color: #1a365d;
  text-align: center;
}

.search-container {
  margin-bottom: 1rem; /* Reduced from 1.5rem */
}

.name-search {
  width: 90% !important; /* Reduced from 100% */
  padding: 0.8rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.filter-options {
  display: flex;
  gap: 1rem;
  width: 90% !important; /* Match other elements */
  margin: 0 auto; /* Center the container */
}

.filter-options select {
  flex: 1;
  padding: 0.8rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.name-card {
  background-color: #f8f9fa;
  padding: 1.2rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.name-card:hover {
  background-color: #f0f4f8;
}

.name-card .name-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.name-card .name-header h4 {
  margin: 0;
}

.name-card .gender {
  background-color: #b2d8b2;
  padding: 0.5em 1em 0.3em 1em;
  border-radius: 15px;
  font-size: 0.9rem;
  color: #666;
  text-transform: capitalize;
}

.name-card p {
  margin-bottom: 0.5rem;
}

.name-actions {
  margin-top: 15px;
}

.name-card .choose-name-btn {
  background-color: #b2d8b2;
  color: white;
  padding: 8px 15px;
  border-radius: 5px;
  text-decoration: none;
  font-weight: bold;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.name-card .choose-name-btn:hover {
  background-color: #b2d8b2;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  border: none;
}

/* Scrollbar styling for all scrollable areas */
.calculator-form::-webkit-scrollbar,
.names-list::-webkit-scrollbar,
.baby-names-form::-webkit-scrollbar {
  width: 8px;
}

.calculator-form::-webkit-scrollbar-track,
.names-list::-webkit-scrollbar-track,
.baby-names-form::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.calculator-form::-webkit-scrollbar-thumb,
.names-list::-webkit-scrollbar-thumb,
.baby-names-form::-webkit-scrollbar-thumb {
  background: #b2d8b2;
  border-radius: 4px;
}

.calculator-form::-webkit-scrollbar-thumb:hover,
.names-list::-webkit-scrollbar-thumb:hover,
.baby-names-form::-webkit-scrollbar-thumb:hover {
  background: #9ac89a;
}

/* Enhanced Responsive Design */
/* Large Desktop (1400px and up) - Default styles already applied */

/* Desktop (1200px - 1399px) */
@media (max-width: 1399px) {
  .wrapper {
    max-width: 1200px;
  }

  .nav-container {
    max-width: 1200px;
    padding: 0.5rem 1rem;
  }

  main {
    max-width: 1000px;
    padding: 2rem 1rem;
  }

  .two-column-layout {
    max-width: 1200px;
  }
}

/* Medium Desktop (992px - 1199px) */
@media (max-width: 1200px) {
  .content-wrapper {
    gap: 2rem;
    padding: 0 1rem;
  }

  .content-box {
    width: 450px;
    height: 450px;
  }

  main {
    padding: 1rem;
  }

  /* Hero section adjustments */
  .slideshow-container {
    height: 350px; /* Reduced from 400px to 350px */
  }

  .slide img {
    height: 350px; /* Reduced from 400px to 350px */
  }

  .slide-caption h2 {
    font-size: 2rem;
  }

  .slide-caption p {
    font-size: 1rem;
  }
}

/* Tablet (768px - 991px) */
@media (max-width: 992px) {
  .content-wrapper {
    flex-direction: column;
    align-items: center;
    gap: 1rem; /* Reduced from 1.5rem */
  }

  .content-box {
    width: 100%;
    max-width: 600px;
    height: auto;
    min-height: 400px;
    margin-bottom: 1rem; /* Reduced from 2rem */
  }

  /* Reduce spacing between main sections */
  .two-column-layout {
    margin: 0.5rem auto !important; /* Reduced further from 1rem */
  }

  /* Reduce hero to main content gap */
  main {
    padding: 0.2rem 0; /* Reduced further from 0.5rem */
  }

  /* Target the specific sections in index.php */
  #faq-community {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  #planning-tools {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .nav-links {
    gap: 0.5rem;
  }

  .nav-links a {
    padding: 0.5rem;
    font-size: 0.95rem;
  }

  /* Hero section for tablets */
  .slideshow-container {
    height: 300px; /* Reduced from 350px to 300px */
  }

  .slide img {
    height: 300px; /* Reduced from 350px to 300px */
  }

  .slide-caption {
    padding: 1rem;
  }

  .slide-caption h2 {
    font-size: 1.8rem;
  }

  .slide-caption p {
    font-size: 0.95rem;
  }

  /* Main content adjustments */
  main {
    padding: 1.5rem 1rem;
  }

  .two-column-layout {
    margin: 2.5rem auto !important;
  }

  /* Form adjustments */
  .calculator-form,
  .baby-names-form {
    padding: 0;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  /* FAQ and Community sections */
  .faq-item,
  .post-item {
    margin-bottom: 1.5rem;
    padding: 1.5rem;
  }
}

/* Mobile Large (576px - 767px) */
@media (max-width: 768px) {
  /* Navigation - hamburger menu already handled above */
  .nav-container {
    padding: 0.8rem 1rem;
  }

  .login-container .user-menu-btn,
  .login-container .login-btn {
    font-size: 0.9rem;
    padding: 0.5rem 0.8rem;
  }

  /* Further reduce spacing on mobile */
  .content-wrapper {
    gap: 0.8rem; /* Reduced further */
  }

  .content-box {
    margin-bottom: 0.8rem; /* Reduced further */
  }

  .two-column-layout {
    margin: 0.3rem auto !important; /* Reduced even further */
  }

  main {
    padding: 0.1rem 0; /* Reduced even further */
  }

  /* Reduce section divider spacing */
  .section-divider {
    margin: 0.3rem auto !important; /* Reduced even further */
  }

  /* Target specific sections for mobile */
  #faq-community {
    margin-top: 0.2rem !important;
    margin-bottom: 0.2rem !important;
  }

  #planning-tools {
    margin-top: 0.2rem !important;
    margin-bottom: 0.2rem !important;
  }

  /* Reduce gap between hero and main content */
  .hero-section {
    margin-bottom: 0 !important;
  }

  /* Hero Section */
  .slideshow-container {
    height: 280px;
  }

  .slide img {
    height: 280px;
  }

  .slide-caption {
    padding: 0.8rem;
    bottom: 10px;
  }

  .slide-caption h2 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .slide-caption p {
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .slide-nav {
    width: 35px;
    height: 35px;
    font-size: 14px;
  }

  .slide-dots {
    bottom: 5px;
  }

  .dot {
    width: 10px;
    height: 10px;
  }

  /* Main Content */
  main {
    padding: 1rem 0.5rem;
  }

  .two-column-layout {
    margin: 2rem auto !important;
    padding: 0 0.5rem;
  }

  .content-box {
    padding: 1.5rem;
    margin: 0 0.5rem 1.5rem 0.5rem;
  }

  .content-box h2 {
    font-size: 1.6rem;
    margin-bottom: 1rem;
  }

  .content-box h3 {
    font-size: 1.3rem;
    margin-bottom: 0.8rem;
  }

  /* Forms and Tools */
  .calculator-form,
  .baby-names-form {
    width: 100%;
    padding: 0;
  }

  .form-group {
    margin-bottom: 1.2rem;
  }

  .form-group label {
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
  }

  input, select, textarea {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 0.8rem;
    width: 100%;
  }

  .calculate-btn,
  .submit-name-btn {
    width: 100%;
    padding: 0.8rem;
    font-size: 1rem;
    margin-top: 1rem;
  }

  .filter-options {
    flex-direction: column;
    gap: 0.5rem;
  }

  /* Community Posts and FAQ */
  .faq-item,
  .post-item {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .faq-item h3,
  .post-item h3 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  /* Name cards */
  .name-card {
    margin-bottom: 1rem;
    padding: 1rem;
  }

  .name-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3rem;
  }

  .choose-name-btn {
    width: 100%;
    text-align: center;
    padding: 0.6rem;
    margin-top: 0.5rem;
  }
}

/* Mobile Small (320px - 575px) */
@media (max-width: 576px) {
  /* Navigation */
  .nav-container {
    padding: 0.6rem 0.5rem;
  }

  .logo {
    font-size: 1.2rem;
  }

  .login-container .user-menu-btn,
  .login-container .login-btn {
    font-size: 0.85rem;
    padding: 0.4rem 0.6rem;
  }

  .hamburger {
    width: 35px;
    height: 35px;
  }

  .hamburger span {
    width: 20px;
    height: 2px;
  }

  /* Minimal spacing on small mobile */
  .content-wrapper {
    gap: 0.5rem; /* Minimal gap */
  }

  .content-box {
    margin-bottom: 0.5rem; /* Minimal margin */
  }

  .two-column-layout {
    margin: 0.2rem auto !important; /* Ultra minimal margin */
  }

  main {
    padding: 0.1rem 0; /* Ultra minimal padding */
  }

  .section-divider {
    margin: 0.2rem auto !important; /* Ultra minimal margin */
  }

  /* Ultra tight spacing for small screens */
  #faq-community {
    margin-top: 0.1rem !important;
    margin-bottom: 0.1rem !important;
  }

  #planning-tools {
    margin-top: 0.1rem !important;
    margin-bottom: 0.1rem !important;
  }

  /* Remove any gap between hero and main */
  .hero-section {
    margin-bottom: 0 !important;
  }

  main {
    margin-top: 0 !important;
  }

  /* Hero Section */
  .slideshow-container {
    height: 240px;
  }

  .slide img {
    height: 240px;
  }

  .slide-nav {
    width: 30px;
    height: 30px;
    font-size: 12px;
  }

  .slide-nav.prev {
    left: 5px;
  }

  .slide-nav.next {
    right: 5px;
  }

  .slide-caption {
    padding: 0.5rem;
    bottom: 5px;
  }

  .slide-caption h2 {
    font-size: 1.2rem;
    margin-bottom: 0.3rem;
  }

  .slide-caption p {
    font-size: 0.8rem;
    line-height: 1.3;
  }

  .dot {
    width: 8px;
    height: 8px;
    margin: 0 3px;
  }

  /* Main Content */
  main {
    padding: 0.8rem 0.3rem;
  }

  .two-column-layout {
    margin: 1.5rem auto !important;
    padding: 0 0.3rem;
  }

  .content-box {
    padding: 1rem;
    margin: 0 0.2rem 1rem 0.2rem;
  }

  .content-box h2 {
    font-size: 1.4rem;
    margin-bottom: 0.8rem;
  }

  .content-box h3 {
    font-size: 1.2rem;
    margin-bottom: 0.6rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  /* Forms */
  .form-group {
    margin-bottom: 1rem;
  }

  .form-group label {
    font-size: 0.9rem;
    margin-bottom: 0.4rem;
  }

  input,
  select,
  textarea {
    padding: 0.6rem;
    font-size: 16px; /* Prevents zoom on iOS */
    border-radius: 6px;
  }

  .calculate-btn,
  .submit-name-btn {
    padding: 0.7rem;
    font-size: 0.95rem;
    border-radius: 6px;
  }

  /* Result boxes */
  .result-box {
    padding: 0.8rem;
    font-size: 0.9rem;
  }

  /* FAQ and Community */
  .faq-item,
  .post-item {
    padding: 0.8rem;
    margin-bottom: 0.8rem;
  }

  .faq-item h3,
  .post-item h3 {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }

  .faq-item p,
  .post-item p {
    font-size: 0.9rem;
    line-height: 1.5;
  }

  .post-meta {
    font-size: 0.8rem;
  }

  /* Name cards */
  .name-card {
    padding: 0.8rem;
    margin-bottom: 0.8rem;
  }

  .name-card h4 {
    font-size: 1.1rem;
  }

  .name-card p {
    font-size: 0.85rem;
  }

  .choose-name-btn {
    padding: 0.5rem;
    font-size: 0.85rem;
  }

  /* Section divider */
  .section-divider {
    margin: 1rem auto !important;
  }
}

/* Extra Small Mobile (320px and below) */
@media (max-width: 320px) {
  .nav-container {
    padding: 0.3rem 0.4rem;
  }

  .logo {
    font-size: 1.1rem;
  }

  .slideshow-container {
    height: 200px;
  }

  .slide img {
    height: 200px;
  }

  .slide-caption h2 {
    font-size: 1rem;
  }

  .slide-caption p {
    font-size: 0.75rem;
  }

  .content-box {
    padding: 0.8rem;
    margin: 0 0.1rem 0.8rem 0.1rem;
  }

  .content-box h2 {
    font-size: 1.2rem;
  }

  .content-box h3 {
    font-size: 1.1rem;
  }

  input,
  select,
  textarea {
    padding: 0.5rem;
    font-size: 16px;
  }

  .faq-item,
  .post-item {
    padding: 0.6rem;
  }

  .name-card {
    padding: 0.6rem;
  }
}

/* Hamburger Menu Styles */
.hamburger {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4px;
  padding: 8px;
  background: none;
  border: none;
  cursor: pointer;
  width: 40px;
  height: 40px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.hamburger:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.hamburger span {
  display: block;
  width: 25px;
  height: 3px;
  background-color: white;
  transition: all 0.3s ease;
  border-radius: 2px;
}

/* Hamburger animation */
.hamburger.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburger.active span:nth-child(2) {
  opacity: 0;
}

.hamburger.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Show hamburger menu on tablets and mobile */
@media (max-width: 992px) {
  .hamburger {
    display: flex;
    position: absolute;
    left: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1002;
  }

  .logo-container {
    position: static;
    transform: none;
    text-align: center;
    flex: 1;
  }

  .login-container {
    position: static;
    transform: none;
  }

  .nav-links {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: #c2d8c2;
    flex-direction: column;
    padding: 1rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border-radius: 0 0 10px 10px;
    z-index: 1001;
  }

  .nav-links.active {
    display: flex;
    animation: slideDown 0.3s ease-out;
  }

  .nav-links a {
    padding: 0.8rem 1rem;
    margin: 0.2rem 0;
    border-radius: 8px;
    width: 100%;
    text-align: center;
    display: block;
  }

  .nav-links a:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: none;
    box-shadow: none;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

.hamburger.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburger.active span:nth-child(2) {
  opacity: 0;
}

.hamburger.active span:nth-child(3) {
  transform: rotate(-45deg) translate(5px, -5px);
}

/* Planning Pregnancy Tools Section */
#planning-tools {
  text-align: center;
  width: 100%;
  margin-top: 0.5rem; /* Reduced from 1rem to 0.5rem */
}

#planning-tools h2 {
  text-align: center;
  margin-bottom: 3rem;
  color: #6b8e23;
  font-size: 1.8rem;
}

#planning-tools .content-wrapper {
  display: flex;
  gap: 4rem;
  justify-content: center; /* Center the content boxes */
  flex-wrap: nowrap;
  width: 100%;
  max-width: 1100px;
  margin: 0 auto;
}

/* Adjust the calculator and baby names boxes */
.due-date-calculator,
.baby-names {
  flex: 0 0 500px;
  height: 500px;
  padding: 2.5rem;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  #planning-tools .content-wrapper {
    flex-direction: column;
    align-items: center;
    gap: 2rem;
  }
}

/* Maintain responsiveness */
@media (max-width: 768px) {
  #main2 h2 {
    margin-bottom: 2rem; /* Slightly less space on mobile */
  }
}

/* Divider between main1 and main2 */
.section-divider {
  width: 90%;
  height: 2px;
  background-color: #b2d8b2;
  margin: 0.5rem auto; /* Reduced from 1rem to 0.5rem */
  opacity: 0.7;
}

/* Maintain responsiveness */
@media (max-width: 768px) {
  .section-divider {
    margin: 0.25rem auto; /* Reduced from 0.75rem to 0.25rem */
  }
}

@media (max-width: 576px) {
  .section-divider {
    width: 95%;
    margin: 0.25rem auto; /* Reduced from 0.5rem to 0.25rem */
  }
}

/* ==========================================================================
   Footer
   ========================================================================== */
footer {
  width: 100%;
  background-color: #b2d8b2;
  margin-top: auto;
  position: relative;
}

.footer-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem 1rem;
  text-align: center;
}

.footer-content p {
  color: white;
  margin: 0;
  font-size: 0.95rem;
  line-height: 1.5;
}

/* Responsive Footer */
@media (max-width: 768px) {
  .footer-content {
    padding: 1.5rem 1rem;
  }

  .footer-content p {
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .footer-content {
    padding: 1rem 0.5rem;
  }

  .footer-content p {
    font-size: 0.85rem;
  }
}

/* ==========================================================================
   Login Modal
   ========================================================================== */
.modal {
  display: none;
  position: fixed;
  z-index: 2000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: rgba(0, 0, 0, 0.6);
  animation: fadeIn 0.4s;
  backdrop-filter: blur(5px);
}

.modal-content {
  background-color: #fff;
  margin: 8% auto;
  padding: 35px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
  width: 90%;
  max-width: 420px;
  position: relative;
  animation: slideDown 0.5s;
  border: 1px solid rgba(178, 216, 178, 0.3);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-70px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.close {
  position: absolute;
  right: 25px;
  top: 20px;
  color: #aaa;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.close:hover {
  color: #555;
  background-color: rgba(0, 0, 0, 0.05);
  transform: rotate(90deg);
}

.modal h2 {
  color: #6b8e23;
  margin-bottom: 25px;
  text-align: center;
  font-size: 2rem;
  font-weight: 700;
  letter-spacing: 0.5px;
}

.modal .message {
  padding: 14px 18px;
  margin-bottom: 20px;
  border-radius: 10px;
  font-size: 1rem;
  line-height: 1.5;
  font-weight: 500;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.modal .message.success {
  background-color: #e8f5e9;
  color: #2e7d32;
  border-left: 4px solid #2e7d32;
}

.modal .message.error {
  background-color: #ffebee;
  color: #c62828;
  border-left: 4px solid #c62828;
}

#login-form,
#register-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

#login-form .form-group,
#register-form .form-group {
  margin-bottom: 22px;
  position: relative;
  width: 100%;
}

#login-form label,
#register-form label {
  display: block;
  margin-bottom: 10px;
  color: #4a5568;
  font-weight: 600;
  font-size: 1.05rem;
  transition: all 0.3s;
  text-align: center;
}

#login-form input[type="text"],
#login-form input[type="email"],
#login-form input[type="password"],
#register-form input[type="text"],
#register-form input[type="email"],
#register-form input[type="password"] {
  width: 90%;
  max-width: 350px;
  padding: 14px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.3s;
  background-color: #f9f9f9;
  display: block;
  margin: 0 auto;
  text-align: center;
}

/* Exclude checkboxes from the above styling */
#login-form input[type="checkbox"],
#register-form input[type="checkbox"] {
  width: auto;
  padding: 0;
}

#login-form input:focus,
#register-form input:focus {
  border-color: #b2d8b2;
  outline: none;
  box-shadow: 0 0 0 4px rgba(178, 216, 178, 0.2);
  background-color: #fff;
}

/* Base Checkbox Styling */
.checkbox-container {
  display: flex;
  position: relative;
  width: 100%;
  margin-bottom: 1.2rem;
}

/* Base styles for checkboxes - not applied to remember-me-container */
.checkbox-container:not(.remember-me-container) input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Base styles for checkbox labels - not applied to remember-me-container */
.checkbox-container:not(.remember-me-container) label {
  display: flex;
  position: relative;
  cursor: pointer;
  user-select: none;
  padding: 0;
  margin: 0;
}

.checkbox-container:not(.remember-me-container) label::before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 10px;
  border: 2px solid #b2d8b2;
  border-radius: 3px;
  background-color: #fff;
  transition: all 0.2s;
  flex-shrink: 0;
}

.checkbox-container:not(.remember-me-container)
  input[type="checkbox"]:checked
  + label::before {
  background-color: #b2d8b2;
}

.checkbox-container:not(.remember-me-container)
  input[type="checkbox"]:checked
  + label::after {
  content: "✓";
  position: absolute;
  left: 5px;
  color: white;
  font-weight: bold;
}

/* No need to hide pseudo-elements anymore as they're excluded by the selectors above */

/* Terms Checkbox Styling - Simplified */
.terms-container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  width: 100%;
  margin-bottom: 1.2rem;
}

.terms-container input[type="checkbox"] {
  width: 20px;
  height: 20px;
  margin: 0;
  margin-right: 10px;
  margin-top: 3px;
  cursor: pointer;
  transform: scale(1.3);
  flex-shrink: 0;
}

.terms-container label {
  font-size: 0.95rem;
  color: #555;
  white-space: normal;
  line-height: 1.4;
  cursor: pointer;
  font-weight: 500;
}

/* Remember Me Checkbox Styling */
.remember-me-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-bottom: 1.2rem;
}

.remember-me-container label {
  font-size: 1rem;
  color: #555;
  white-space: normal;
  line-height: 1.4;
  cursor: pointer;
  font-weight: 600;
  text-align: center;
  margin-bottom: 10px;
  transition: color 0.3s ease;
}

/* Special handling for the label color when checkbox is checked */
.remember-me-container input[type="checkbox"]:checked ~ label {
  color: #6b8e23;
}

/* JavaScript will add this class when checkbox is focused */
.remember-me-container label.focused {
  color: #6b8e23;
}

/* Custom checkbox container */
.custom-checkbox {
  position: relative;
  display: inline-block;
  width: 30px;
  height: 30px;
  cursor: pointer; /* Add cursor pointer to the container */
}

/* Style the actual checkbox input */
.custom-checkbox input {
  position: absolute;
  opacity: 0; /* Make it invisible but still functional */
  width: 30px; /* Match the size of the custom checkbox */
  height: 30px;
  cursor: pointer;
  z-index: 2; /* Place it above the custom checkbox */
  margin: 0;
  padding: 0;
}

/* Create a custom checkbox */
.custom-checkbox .checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 30px;
  width: 30px;
  background-color: #f9f9f9;
  border: 2px solid #ddd;
  border-radius: 4px;
  box-sizing: border-box;
  transition: all 0.3s ease;
  z-index: 1; /* Place it below the input */
  pointer-events: none; /* Ensure clicks go through to the input */
}

/* On mouse-over */
.custom-checkbox:hover .checkmark {
  border-color: #b2d8b2;
  background-color: #f5f9f5;
}

/* When the checkbox is checked */
.custom-checkbox input:checked ~ .checkmark {
  background-color: #6b8e23;
  border-color: #6b8e23;
  box-shadow: 0 0 0 2px rgba(107, 142, 35, 0.3);
}

/* When the checkbox is focused */
.custom-checkbox input:focus ~ .checkmark {
  border-color: #6b8e23;
  box-shadow: 0 0 0 3px rgba(107, 142, 35, 0.3);
}

/* Create the checkmark/indicator (hidden when not checked) */
.custom-checkbox .checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.custom-checkbox input:checked ~ .checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.custom-checkbox .checkmark:after {
  left: 10px;
  top: 5px;
  width: 6px;
  height: 12px;
  border: solid white;
  border-width: 0 3px 3px 0;
  transform: rotate(45deg);
}

.login-submit-btn,
.register-submit-btn {
  width: 80%;
  max-width: 300px;
  background-color: #b2d8b2;
  color: white;
  padding: 14px;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 15px;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 10px rgba(178, 216, 178, 0.4);
  position: relative;
  overflow: hidden;
  align-self: center;
}

.login-submit-btn:hover,
.register-submit-btn:hover {
  background-color: #9ac89a;
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(178, 216, 178, 0.5);
}

.login-submit-btn:active,
.register-submit-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 5px rgba(178, 216, 178, 0.4);
}

/* Add loading animation for buttons */
.login-submit-btn.loading,
.register-submit-btn.loading {
  position: relative;
  color: transparent;
}

.login-submit-btn.loading::after,
.register-submit-btn.loading::after {
  content: "";
  position: absolute;
  width: 20px;
  height: 20px;
  top: 50%;
  left: 50%;
  margin-left: -10px;
  margin-top: -10px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.form-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 25px;
  font-size: 0.95rem;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
  width: 100%;
}

.form-footer a {
  color: #6b8e23;
  text-decoration: none;
  transition: all 0.3s;
  font-weight: 500;
  position: relative;
}

.form-footer a:hover {
  color: #556b2f;
}

.form-footer a::after {
  content: "";
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: #6b8e23;
  transition: width 0.3s;
}

.form-footer a:hover::after {
  width: 100%;
}

/* Registration form specific styles */
.checkbox-container.terms {
  margin-top: 15px;
  margin-bottom: 20px;
}

.checkbox-container a {
  color: #6b8e23;
  text-decoration: none;
  font-weight: 500;
  position: relative;
  transition: all 0.3s;
}

.checkbox-container a:hover {
  color: #556b2f;
}

.checkbox-container a::after {
  content: "";
  position: absolute;
  width: 0;
  height: 1px;
  bottom: 0;
  left: 0;
  background-color: #6b8e23;
  transition: width 0.3s;
}

.checkbox-container a:hover::after {
  width: 100%;
}

.form-text {
  display: block;
  margin-top: 5px;
  font-size: 0.9rem;
  color: #666;
  line-height: 1.5;
  font-style: italic;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */
@media (max-width: 1024px) {
  /* Add any responsive styles needed */
}

@media (max-width: 600px) {
  .nav-links {
    display: none;
  }

  .hamburger {
    display: block;
  }

  .modal-content {
    margin: 15% auto;
    padding: 25px 20px;
    width: 95%;
  }

  .modal h2 {
    font-size: 1.7rem;
    margin-bottom: 20px;
  }

  #login-form input[type="text"],
  #login-form input[type="email"],
  #login-form input[type="password"],
  #register-form input[type="text"],
  #register-form input[type="email"],
  #register-form input[type="password"] {
    padding: 12px 14px;
  }

  .login-submit-btn,
  .register-submit-btn {
    padding: 12px;
  }

  .form-footer {
    flex-direction: column;
    gap: 15px;
    align-items: center;
  }

  .terms-container {
    justify-content: center;
  }

  #login-form input[type="text"],
  #login-form input[type="email"],
  #login-form input[type="password"],
  #register-form input[type="text"],
  #register-form input[type="email"],
  #register-form input[type="password"] {
    width: 100%;
    max-width: 100%;
  }

  #login-form label,
  #register-form label {
    text-align: center;
  }
}

/* Due Date Calculator Form Styles */
.calculator-form .form-group {
  margin-bottom: 1.5rem;
}

.calculator-form input[type="date"],
.calculator-form select {
  width: 90%; /* Same width for both inputs */
  padding: 0.8rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  background-color: white;
  box-sizing: border-box; /* Ensure padding is included in width */
  color: #333;
}

.calculator-form select {
  appearance: none; /* Remove default select styling */
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' fill='%234a5568' viewBox='0 0 16 16'%3E%3Cpath d='M8 11L3 6h10l-5 5z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.8rem center;
  padding-right: 2.5rem; /* Space for the arrow */
}

.calculator-form .form-group {
  margin-bottom: 1.5rem;
  width: 100%;
}

/* Due date calculator and baby names boxes */
.due-date-calculator,
.baby-names {
  flex: 0 0 700px !important;
  min-height: 700px !important;
  height: 700px !important;
  max-height: 700px !important;
  padding: 2rem !important;
  border-radius: 15px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background: white;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow-y: auto;
}

/* Maintain hover effect */
.due-date-calculator:hover,
.baby-names:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
  background-color: rgba(255, 255, 255, 0.95);
  color: #333;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .due-date-calculator,
  .baby-names {
    width: 100% !important;
    max-width: 600px !important;
    height: 500px !important;
    min-height: 500px !important;
    margin-bottom: 2rem;
  }
}

/* Responsive Utilities */
/* Hide elements on mobile */
@media (max-width: 768px) {
  .hide-mobile {
    display: none !important;
  }
}

/* Show only on mobile */
.show-mobile {
  display: none;
}

@media (max-width: 768px) {
  .show-mobile {
    display: block;
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  /* Touch devices */
  .nav-links a,
  .calculate-btn,
  .submit-name-btn,
  .choose-name-btn,
  .login-btn,
  .user-menu-btn {
    min-height: 44px; /* Apple's recommended touch target size */
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Remove hover effects on touch devices */
  .nav-links a:hover,
  .content-box:hover,
  .due-date-calculator:hover,
  .baby-names:hover {
    transform: none;
    box-shadow: initial;
  }

  /* Add active states for touch feedback */
  .nav-links a:active,
  .calculate-btn:active,
  .submit-name-btn:active,
  .choose-name-btn:active {
    transform: scale(0.98);
    opacity: 0.8;
  }
}

/* Improve focus states for accessibility */
.nav-links a:focus,
.calculate-btn:focus,
.submit-name-btn:focus,
.choose-name-btn:focus,
.login-btn:focus,
.user-menu-btn:focus,
input:focus,
select:focus,
textarea:focus,
.hamburger:focus {
  outline: 2px solid #6b8e23;
  outline-offset: 2px;
}

/* Responsive Images */
img {
  max-width: 100%;
  height: auto;
}

/* Responsive Tables */
@media (max-width: 768px) {
  table {
    font-size: 0.9rem;
  }

  table, thead, tbody, th, td, tr {
    display: block;
  }

  thead tr {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }

  tr {
    border: 1px solid #ccc;
    margin-bottom: 10px;
    padding: 10px;
    border-radius: 8px;
    background: white;
  }

  td {
    border: none;
    position: relative;
    padding-left: 50% !important;
    padding-top: 10px;
    padding-bottom: 10px;
  }

  td:before {
    content: attr(data-label) ": ";
    position: absolute;
    left: 6px;
    width: 45%;
    padding-right: 10px;
    white-space: nowrap;
    font-weight: bold;
    color: #6b8e23;
  }
}

/* Responsive Typography */
@media (max-width: 768px) {
  body {
    font-size: 1rem;
    line-height: 1.5;
  }

  h1 {
    font-size: 1.8rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  h3 {
    font-size: 1.3rem;
  }

  h4 {
    font-size: 1.1rem;
  }

  p {
    font-size: 0.95rem;
  }
}

@media (max-width: 576px) {
  body {
    font-size: 0.95rem;
  }

  h1 {
    font-size: 1.6rem;
  }

  h2 {
    font-size: 1.4rem;
  }

  h3 {
    font-size: 1.2rem;
  }

  h4 {
    font-size: 1rem;
  }

  p {
    font-size: 0.9rem;
  }
}

/* Performance Optimizations */
/* Reduce animations on low-end devices */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Optimize for touch devices */
@media (pointer: coarse) {
  /* Increase touch targets */
  .nav-links a,
  .calculate-btn,
  .submit-name-btn,
  .choose-name-btn,
  .login-btn,
  .user-menu-btn,
  .slide-nav,
  .dot {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improve button spacing on mobile */
  .nav-links a {
    margin: 0.2rem 0;
  }

  /* Better spacing for form elements */
  .form-group {
    margin-bottom: 1.5rem;
  }
}

/* Responsive Utilities for Content Management */
.container-fluid {
  width: 100%;
  padding: 0 15px;
  margin: 0 auto;
}

@media (min-width: 576px) {
  .container-fluid {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container-fluid {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container-fluid {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container-fluid {
    max-width: 1140px;
  }
}

@media (min-width: 1400px) {
  .container-fluid {
    max-width: 1320px;
  }
}

/* Mobile-First Loading States */
@media (max-width: 768px) {
  /* Skeleton loading for better perceived performance */
  .loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }

  @keyframes loading {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
}

/* Common transition for all interactive elements */
.nav-links a,
.logo,
.login-btn,
.user-menu-btn,
.content-box,
.due-date-calculator,
.baby-names,
.user-dropdown a,
.calculate-btn,
.submit-name-btn,
.choose-name-btn {
  transition: all 0.3s ease;
}

/* Final Mobile Optimizations */
@media (max-width: 768px) {
  /* Prevent horizontal scroll */
  body {
    overflow-x: hidden;
  }

  /* Optimize viewport for mobile */
  .wrapper {
    min-width: 320px;
  }

  /* Better mobile form styling */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="date"],
  select,
  textarea {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 8px;
    border: 2px solid #ddd;
    padding: 12px;
    font-size: 16px; /* Prevents zoom on iOS */
    width: 100%;
    box-sizing: border-box;
  }

  input[type="text"]:focus,
  input[type="email"]:focus,
  input[type="password"]:focus,
  input[type="date"]:focus,
  select:focus,
  textarea:focus {
    border-color: #6b8e23;
    box-shadow: 0 0 0 3px rgba(107, 142, 35, 0.1);
  }

  /* Mobile-optimized buttons */
  .calculate-btn,
  .submit-name-btn,
  .choose-name-btn {
    padding: 12px 20px;
    font-size: 16px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    width: 100%;
    margin-top: 10px;
  }

  /* Improve mobile navigation */
  .nav-links.active {
    animation: slideDown 0.3s ease-out;
  }

  @keyframes slideDown {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

/* Hover effects */
.nav-links a:hover,
.login-btn:hover {
  /* Added login-btn here to ensure consistent behavior */
  background-color: rgba(255, 255, 255, 0.95);
  color: #1a2e1a; /* Very dark green, almost black */
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.page-header {
  padding: 2rem;
  margin: 0 auto;
  max-width: 1200px; /* or whatever width matches your white box */
  width: 100%;
}

.page-header h1 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-header p {
  margin-top: 0.5rem;
}

.agenda-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 5px; /* Reduced from 10px */
  min-height: calc(100vh - 400px); /* Reduced from calc(100vh - 300px) */
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-prompt {
  text-align: center;
  background-color: white;
  padding: 40px 50px; /* Increased padding, more on sides */
  border-radius: 15px;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12); /* Enhanced shadow */
  width: 100%;
  max-width: 600px; /* Increased from 500px */
  margin: auto;
}

.login-prompt h2 {
  color: #6b8e23;
  margin-bottom: 15px;
  font-size: 2rem; /* Increased from 1.8rem */
  font-weight: 600; /* Made font weight bolder */
}

.login-prompt p {
  color: #4a5568;
  margin-bottom: 25px; /* Increased from 15px */
  font-size: 1.2rem; /* Increased from 1.1rem */
  line-height: 1.5;
}

.login-prompt-btn {
  display: inline-block;
  background-color: #b2d8b2;
  color: white;
  padding: 12px 35px 8px 35px; /* Reduced bottom padding from 12px to 8px */
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.login-prompt-btn:hover {
  background-color: #9ac89a;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Health Page Styles */
.health-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 5px;
  min-height: calc(100vh - 400px); /* Updated to match agenda and baby pages */
  display: flex;
  align-items: center;
  justify-content: center;
}

.health-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.health-header h1 {
  margin: 0;
  color: #6b8e23;
}

/* Content Box Styles */
.content-section {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.content-wrapper {
  display: flex;
  gap: 30px;
}

.content-box {
  flex: 1;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.content-box h2 {
  color: #6b8e23;
  margin-bottom: 20px;
}

/* Symptom Card Styles */
.symptom-card {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.symptom-card h3 {
  color: #6b8e23;
  margin-top: 0;
  margin-bottom: 10px;
}

.symptom-card .date {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.symptom-card .severity {
  display: inline-block;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.9rem;
  margin-bottom: 15px;
}

.severity.mild {
  background-color: #e8f5e9;
  color: #2e8b57;
}

.severity.moderate {
  background-color: #fff3e0;
  color: #f57c00;
}

.severity.severe {
  background-color: #ffebee;
  color: #c62828;
}

.tip-box {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
}

.tip-box h4 {
  color: #6b8e23;
  margin-top: 0;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #333;
  font-weight: 500;
}

.form-group select,
.form-group input[type="date"],
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
}

.form-group textarea {
  resize: vertical;
  min-height: 100px;
}

.submit-btn {
  background-color: #b2d8b2; /* Changed from #6b8e23 */
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
}

.submit-btn:hover {
  background-color: #9ac89a; /* Changed from #556b2f */
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Message Styles */
.message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 25px;
  border-radius: 8px;
  font-weight: 500;
  z-index: 1000;
  max-width: 350px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideIn 0.3s ease-out forwards;
}

.message.success {
  background-color: #e8f5e9;
  color: #2e7d32;
  border-left: 4px solid #2e7d32;
}

.message.error {
  background-color: #ffebee;
  color: #c62828;
  border-left: 4px solid #c62828;
}

/* Add animation keyframes */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* Add a close button to messages */
.message .close-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 5px;
  font-size: 18px;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.message .close-btn:hover {
  opacity: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .content-wrapper {
    flex-direction: column;
  }

  .content-box {
    width: 100%;
  }
}

/* Appointments Page Styles */
.appointments-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 5px;
  min-height: calc(100vh - 400px);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Update existing appointment styles to work within the new container */
.content-section {
  width: 100%;
  max-width: 1200px;
  background-color: white;
  padding: 40px 50px;
  border-radius: 15px;
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
}

.content-wrapper {
  display: flex;
  gap: 30px;
}

.content-box {
  flex: 1;
  background-color: white;
  border-radius: 10px;
  padding: 20px;
}

.baby-names .main-heading {
  color: #6b8e23;
  font-size: 2rem;
  font-weight: 500;
  text-align: center;
  margin-bottom: 0.5rem;
}

.baby-names .subtitle {
  color: #666;
  font-size: 1rem;
  text-align: center;
  margin-bottom: 2rem;
  font-weight: normal;
}

.due-date-calculator h3 {
  color: #6b8e23; /* Same green color used in baby names */
  font-size: 2rem;
  font-weight: 500;
  text-align: center;
  margin-bottom: 0.5rem;
}

/* Add this to your existing CSS */
.result-box {
  margin-top: 2rem;
  padding: 1em;
  background-color: #f8f9fa;
  border-radius: 8px;
}

/* Category selection in agenda modal */
.category-selection {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.category-option {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.category-option:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border-color: #2c5282;
}

.category-link {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  text-decoration: none;
  color: #333;
}

.category-link i {
  font-size: 2rem;
  margin-bottom: 10px;
  color: #2c5282;
}

.category-link h3 {
  margin: 10px 0;
  font-size: 1.2rem;
}

.category-link p {
  text-align: center;
  font-size: 0.9rem;
  color: #666;
}
  text-align: center;
}

.result-box p {
  margin: 0;
  color: #4a5568;
  font-size: 1.1rem;
}

#due-date {
  font-weight: 600;
  color: #2d3748;
  display: block;
  margin-top: 0.5rem;
}

/* Agenda Item Styling */
.agenda-item {
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  padding: 20px;
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
  transition: transform 0.2s ease;
}

.agenda-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.12);
}

.agenda-item .date-info {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.agenda-item .date-number {
  font-size: 1.8rem;
  font-weight: bold;
  color: #2c5282;
  margin-right: 10px;
}

.agenda-item .date-details {
  display: flex;
  flex-direction: column;
}

.agenda-item .symptom-title {
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.agenda-item .intensity {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.intensity.severe {
  background-color: #ffebee;
  color: #c62828;
}

.agenda-item .health-tip {
  color: #555;
  font-style: italic;
  margin-top: 5px;
}

.agenda-controls {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
}

.agenda-controls button {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  transition: color 0.2s;
}

.agenda-controls button:hover {
  color: #2c5282;
}

/* View toggle buttons */
.view-toggle {
  display: flex;
  margin-bottom: 15px;
  gap: 10px;
}

.view-toggle button {
  padding: 8px 15px;
  background-color: #f2f2f2;
  border: 1px solid #ddd;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.2s;
}

.view-toggle button.active {
  background-color: #b2d8b2;
  color: white;
}
