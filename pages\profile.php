<?php
    // Set page title and active page
    $pageTitle  = 'User Profile';
    $activePage = 'profile';

    // Additional CSS for the profile page
    $additionalHeadContent = <<<EOT
<style>
    .profile-section {
        display: flex;
        flex-wrap: wrap;
        gap: 2rem;
        justify-content: center;
        margin-bottom: 2rem;
    }

    .profile-box {
        flex: 1;
        min-width: 300px;
        max-width: 500px;
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        padding: 2rem;
    }

    .profile-box h2 {
        color: #6b8e23;
        margin-bottom: 1.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #b2d8b2;
    }

    .profile-info {
        margin-bottom: 1.5rem;
    }

    .profile-info p {
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
    }

    .profile-info i {
        width: 25px;
        color: #6b8e23;
        margin-right: 10px;
    }

    .message {
        padding: 10px 15px;
        margin-bottom: 20px;
        border-radius: 4px;
    }

    .message.success {
        background-color: #e8f5e9;
        color: #2e7d32;
        border-left: 4px solid #2e7d32;
    }

    .message.error {
        background-color: #ffebee;
        color: #c62828;
        border-left: 4px solid #c62828;
    }
</style>
EOT;

    // Start session if not already started
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    // Include the user functions
    require_once __DIR__ . '/../includes/user_functions.php';

    // Check if user is logged in
    if (! isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true) {
        // Redirect to home page if not logged in
        header('Location: ../index.php');
        exit;
    }

    // Get user data
    $userId = $_SESSION['user_id'];
    $user   = getUserById($userId);

    // Initialize variables
    $message     = '';
    $messageType = '';

    // Process form submission for profile update
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
        if ($_POST['action'] === 'update_profile') {
            $name  = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_SPECIAL_CHARS);
            $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);

            if ($name && $email) {
                // Get the current username from user data
                $username = $user['username'];
                if (updateUser($userId, $name, $username, $email)) {
                    // Update session data
                    $_SESSION['user_name']  = $name;
                    $_SESSION['user_email'] = $email;

                    // Update user data
                    $user = getUserById($userId);

                    $message     = "Profile updated successfully!";
                    $messageType = "success";
                } else {
                    $message     = "Error updating profile.";
                    $messageType = "error";
                }
            } else {
                $message     = "Please fill in all required fields.";
                $messageType = "error";
            }
        } elseif ($_POST['action'] === 'change_password') {
            $currentPassword = $_POST['current_password'];
            $newPassword     = $_POST['new_password'];
            $confirmPassword = $_POST['confirm_password'];

            if ($newPassword !== $confirmPassword) {
                $message     = "New passwords do not match.";
                $messageType = "error";
            } elseif (empty($currentPassword) || empty($newPassword)) {
                $message     = "Please fill in all password fields.";
                $messageType = "error";
            } else {
                if (changePassword($userId, $currentPassword, $newPassword)) {
                    $message     = "Password changed successfully!";
                    $messageType = "success";
                } else {
                    $message     = "Current password is incorrect.";
                    $messageType = "error";
                }
            }
        }
    }

    // Include header
    include __DIR__ . '/../includes/header.php';
?>

<main>
    <section class="page-header">
        <h1><i class="fas fa-user-circle"></i> User Profile</h1>
        <p>Manage your account information and settings.</p>
    </section>

    <?php if (! empty($message)): ?>
        <div class="message<?php echo $messageType; ?>">
            <?php echo $message; ?>
        </div>
    <?php endif; ?>

    <section class="profile-section">
        <div class="profile-box">
            <h2>Profile Information</h2>

            <div class="profile-info">
                <p><i class="fas fa-user"></i> <strong>Name:</strong>                                                                      <?php echo htmlspecialchars($user['name']); ?></p>
                <p><i class="fas fa-envelope"></i> <strong>Email:</strong>                                                                           <?php echo htmlspecialchars($user['email']); ?></p>
                <p><i class="fas fa-calendar-alt"></i> <strong>Member Since:</strong>                                                                                      <?php echo date('F j, Y', strtotime($user['created_at'])); ?></p>
            </div>

            <h3>Edit Profile</h3>
            <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                <input type="hidden" name="action" value="update_profile">

                <div class="form-group">
                    <label for="name">Name:</label>
                    <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($user['name']); ?>" required>
                </div>

                <div class="form-group">
                    <label for="email">Email:</label>
                    <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                </div>

                <button type="submit" class="submit-btn">Update Profile</button>
            </form>
        </div>

        <div class="profile-box">
            <h2>Security</h2>

            <h3>Change Password</h3>
            <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                <input type="hidden" name="action" value="change_password">

                <div class="form-group">
                    <label for="current_password">Current Password:</label>
                    <input type="password" id="current_password" name="current_password" required>
                </div>

                <div class="form-group">
                    <label for="new_password">New Password:</label>
                    <input type="password" id="new_password" name="new_password" required>
                </div>

                <div class="form-group">
                    <label for="confirm_password">Confirm New Password:</label>
                    <input type="password" id="confirm_password" name="confirm_password" required>
                </div>

                <button type="submit" class="submit-btn">Change Password</button>
            </form>
        </div>
    </section>
</main>

<?php include __DIR__ . '/../includes/footer.php'; ?>