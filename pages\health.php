<?php
// Include authentication check
require_once __DIR__ . '/../includes/auth_check.php';

// Include the health functions
require_once __DIR__ . '/../includes/health_functions.php';

// Set page title and active page
$pageTitle  = 'Saúde';
$activePage = 'health';

// Include header
include __DIR__ . '/../includes/header.php';

// Set a default user ID for demonstration
$userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 4; // This would normally come from the logged-in user's session

// Process form submission
$message     = '';
$messageType = '';

// Get all available symptoms
$availableSymptoms = getAllSymptoms();

// Get user's logged symptoms
$userSymptoms = getUserSymptoms($userId);

// Process symptom logging
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        // Log new symptom
        if ($_POST['action'] === 'log') {
            $healthId   = filter_input(INPUT_POST, 'health_id', FILTER_VALIDATE_INT);
            $severity   = filter_input(INPUT_POST, 'severity', FILTER_SANITIZE_SPECIAL_CHARS);
            $dateLogged = filter_input(INPUT_POST, 'date_logged', FILTER_SANITIZE_SPECIAL_CHARS);
            $notes      = filter_input(INPUT_POST, 'notes', FILTER_SANITIZE_SPECIAL_CHARS);

            if ($healthId && $severity && $dateLogged) {
                if (logSymptomWithAgenda($userId, $healthId, $severity, $dateLogged, $notes)) {
                    $message     = "Symptom successfully recorded and added to your agenda!";
                    $messageType = "success";

                    // Refresh user symptoms
                    $userSymptoms = getUserSymptoms($userId);
                } else {
                    $message     = "Error recording symptom.";
                    $messageType = "error";
                }
            } else {
                $message     = "Please fill in all required fields.";
                $messageType = "error";
            }
        }

        // Delete symptom log
        if ($_POST['action'] === 'delete' && isset($_POST['symptom_id'])) {
            $symptomId = filter_input(INPUT_POST, 'symptom_id', FILTER_VALIDATE_INT);

            if ($symptomId && deleteSymptomLog($symptomId)) {
                $message     = "Symptom record successfully deleted!";
                $messageType = "success";

                // Refresh user symptoms
                $userSymptoms = getUserSymptoms($userId);
            } else {
                $message     = "Error deleting symptom record.";
                $messageType = "error";
            }
        }
    }
}
?>

<?php
// Additional CSS for health page
$additionalHeadContent = <<<EOT
<style>
    .symptom-card {
        background-color: #f9f9f9;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .symptom-card h3 {
        margin-top: 0;
        color: #6b8e23;
    }

    .symptom-card .date {
        color: #666;
        font-size: 0.9rem;
        margin-bottom: 10px;
    }

    .symptom-card .severity {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        margin-bottom: 10px;
    }

    .severity.mild {
        background-color: #e6f7e6;
        color: #2e8b57;
    }

    .severity.moderate {
        background-color: #fff8e1;
        color: #f9a825;
    }

    .severity.severe {
        background-color: #ffebee;
        color: #c62828;
    }

    .tip-box {
        background-color: #e8f5e9;
        border-left: 4px solid #6b8e23;
        padding: 10px 15px;
        margin-top: 10px;
    }

    .tip-box h4 {
        margin-top: 0;
        color: #2e7d32;
    }

    .symptom-actions {
        margin-top: 15px;
        text-align: right;
    }

    .symptom-actions button {
        background-color: #f44336;
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
    }

    .message {
        padding: 10px 15px;
        margin-bottom: 20px;
        border-radius: 4px;
    }

    .message.success {
        background-color: #e8f5e9;
        color: #2e7d32;
        border-left: 4px solid #2e7d32;
    }

    .message.error {
        background-color: #ffebee;
        color: #c62828;
        border-left: 4px solid #c62828;
    }
</style>
EOT;
?>

<main>
    <section class="health-container">
        <?php if (! empty($message)): ?>
            <div class="message<?php echo $messageType; ?>">
                <?php echo $message; ?>
                <button class="close-btn" onclick="this.parentElement.remove();">&times;</button>
            </div>
        <?php endif; ?>

        <?php if ($isLoggedIn): ?>
            <section class="content-section">
                <div class="content-wrapper">
                    <div class="content-box">
                        <h2>Record New Symptom</h2>
                        <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                            <input type="hidden" name="action" value="log">

                            <div class="form-group">
                                <label for="health_id">Symptom:</label>
                                <select id="health_id" name="health_id" required>
                                    <option value="">Select a symptom</option>
                                    <?php foreach ($availableSymptoms as $symptom): ?>
                                        <option value="<?php echo $symptom['id']; ?>"><?php echo htmlspecialchars($symptom['symptom_name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="severity">Intensity:</label>
                                <select id="severity" name="severity" required>
                                    <option value="Mild">Mild</option>
                                    <option value="Moderate">Moderate</option>
                                    <option value="Severe">Severe</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="date_logged">Date:</label>
                                <input type="date" id="date_logged" name="date_logged" required max="<?php echo date('Y-m-d'); ?>">
                            </div>

                            <div class="form-group">
                                <label for="notes">Notes:</label>
                                <textarea id="notes" name="notes" rows="3" placeholder="Describe details about the symptom..."></textarea>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="submit-btn">Record Symptom</button>
                            </div>
                        </form>
                    </div>

                    <div class="content-box">
                        <h2>Your Recorded Symptoms</h2>
                        <?php if (empty($userSymptoms)): ?>
                            <p>You haven't recorded any symptoms yet.</p>
                        <?php else: ?>
                            <?php foreach ($userSymptoms as $symptom): ?>
                                <div class="symptom-card">
                                    <h3><?php echo htmlspecialchars($symptom['symptom_name']); ?></h3>
                                    <div class="date">
                                        <i class="far fa-calendar-alt"></i>
                                        <?php
                                        $timestamp = strtotime($symptom['date_logged']);
                                        $day       = date('d', $timestamp);
                                        $monthNum  = date('n', $timestamp);
                                        $year      = date('Y', $timestamp);
                                        $monthAbbr = ['', 'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                                        echo $day . ' ' . $monthAbbr[$monthNum] . ' ' . $year;
                                        ?>
                                    </div>
                                    <span class="severity                                                          <?php echo strtolower($symptom['severity']); ?>">
                                        <?php echo htmlspecialchars($symptom['severity']); ?>
                                    </span>
                                    <?php if (! empty($symptom['notes'])): ?>
                                        <p><?php echo htmlspecialchars($symptom['notes']); ?></p>
                                    <?php endif; ?>
                                    <?php if (! empty($symptom['tip'])): ?>
                                        <div class="tip-box">
                                            <h4><i class="fas fa-lightbulb"></i> Tip</h4>
                                            <p><?php echo htmlspecialchars($symptom['tip']); ?></p>
                                        </div>
                                    <?php endif; ?>
                                    <div class="symptom-actions">
                                        <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" onsubmit="return confirm('Are you sure you want to delete this record?');">
                                            <input type="hidden" name="action" value="delete">
                                            <input type="hidden" name="symptom_id" value="<?php echo $symptom['id']; ?>">
                                            <button type="submit" class="delete-btn"><i class="fas fa-trash"></i> Delete</button>
                                        </form>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </section>
        <?php else: ?>
            <div class="login-prompt">
                <h2>Access Your Health Information</h2>
                <p>Please log in to view and manage your health records.</p>
                <a href="#" class="login-trigger login-prompt-btn">Login</a>
            </div>
        <?php endif; ?>
    </section>
</main>

<?php include __DIR__ . '/../includes/footer.php'; ?>
</div>

<script src="../assets/js/script.js"></script>