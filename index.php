<?php
// Set page title
$pageTitle  = 'Início';
$activePage = 'home';

// Include header
include __DIR__ . '/includes/header.php';

// Include database connection
require_once 'includes/database.php';

// Include baby functions
require_once 'includes/baby_functions.php';

// Populate baby names table if empty
populateBabyNamesIfEmpty();

// Process search form submission
$searchResults   = [];
$searchPerformed = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['search-names'])) {
    $searchPerformed = true;
    $name            = isset($_POST['name']) ? trim($_POST['name']) : '';
    $gender          = isset($_POST['gender']) ? trim($_POST['gender']) : 'all';
    $meaning         = isset($_POST['meaning']) ? trim($_POST['meaning']) : '';

    // Search for baby names
    $searchResults = searchBabyNames($name, $gender, null, $meaning);
}
?>

<header class="hero-section">
    <div class="slideshow-container">
        <button class="slide-nav prev" aria-label="Slide anterior">&#10094;</button>
        <button class="slide-nav next" aria-label="Próximo slide">&#10095;</button>

        <div class="slide fade" aria-label="Slide 1">
            <img src="https://images.unsplash.com/photo-1590649880765-91b1956b8276" alt="Mãe segurando sua barriga grávida">
            <div class="slide-caption">
                <h2>Apoiando Sua Jornada</h2>
                <p>Orientação amigável através de cada etapa da sua gravidez</p>
            </div>
        </div>

        <div class="slide fade" aria-label="Slide 2">
            <img src="https://images.unsplash.com/photo-1531983412531-1f49a365ffed" alt="Mulher grávida fazendo yoga">
            <div class="slide-caption">
                <h2>Gravidez Saudável</h2>
                <p>Dicas simples e conselhos personalizados para o seu bem-estar</p>
            </div>
        </div>

        <div class="slide fade" aria-label="Slide 3">
            <img src="https://images.unsplash.com/photo-1522771930-78848d9293e8" alt="Grupo de futuras mães">
            <div class="slide-caption">
                <h2>Apoio da Comunidade</h2>
                <p>Compartilhe e conecte-se com outros pais na mesma jornada</p>
            </div>
        </div>

        <div class="slide-dots" role="tablist">
            <button class="dot" role="tab" aria-label="Slide 1"></button>
            <button class="dot" role="tab" aria-label="Slide 2"></button>
            <button class="dot" role="tab" aria-label="Slide 3"></button>
        </div>
    </div>
</header>

<main>
    <section id="faq-community" class="two-column-layout">
        <div class="content-wrapper">
            <section class="faq-section content-box">
                <h2>Suas Dúvidas sobre Gravidez Respondidas</h2>
                <div class="faq-content">
                    <div class="faq-item">
                        <h3>Quando devo consultar um médico pela primeira vez?</h3>
                        <p>É melhor agendar sua primeira consulta pré-natal assim que descobrir que está grávida, idealmente nas primeiras 8 semanas. Este cuidado precoce ajuda a garantir um início saudável para você e seu bebê.</p>
                    </div>
                    <div class="faq-item">
                        <h3>Que alimentos devo evitar?</h3>
                        <p>Para sua segurança e a saúde do seu bebê, evite peixe cru (como sushi), produtos lácteos não pasteurizados, ovos crus ou mal cozidos, e peixes com alto teor de mercúrio como tubarão, peixe-espada e cavala durante a gravidez.</p>
                    </div>
                    <div class="faq-item">
                        <h3>Quanto ganho de peso é normal?</h3>
                        <p>O ganho de peso saudável durante a gravidez varia de pessoa para pessoa. Para aquelas com IMC normal antes da gravidez, um ganho de 11-16 kg é tipicamente recomendado, mas seu profissional de saúde pode dar orientação personalizada.</p>
                    </div>
                    <div class="faq-item">
                        <h3>Quando vou sentir meu bebê se mexer?</h3>
                        <p>O momento emocionante de sentir os primeiros movimentos do seu bebê (chamado de "primeiros movimentos") geralmente acontece entre 18-20 semanas para mães de primeira viagem. Se você já esteve grávida antes, pode notar esses movimentos suaves um pouco mais cedo, por volta de 16-18 semanas.</p>
                    </div>
                </div>
            </section>

            <section class="community-section content-box">
                <h2>Postagens Recentes da Comunidade</h2>
                <div class="community-content">
                    <div class="post-item">
                        <h3>Dicas para Enjoo Matinal</h3>
                        <p class="post-meta">Postado por Sarah • 2 horas atrás</p>
                        <p>Descobri que chá de gengibre realmente ajuda com enjoo matinal...</p>
                    </div>
                    <div class="post-item">
                        <h3>Exercícios Seguros no Terceiro Trimestre</h3>
                        <p class="post-meta">Postado por Emma • 5 horas atrás</p>
                        <p>Procurando exercícios suaves que sejam seguros no meu último trimestre. Alguma sugestão?</p>
                    </div>
                    <div class="post-item">
                        <h3>Essenciais da Minha Lista de Bebê</h3>
                        <p class="post-meta">Postado por Lisa • 1 dia atrás</p>
                        <p>Montei uma lista de itens indispensáveis que tornaram nossas primeiras semanas com o bebê muito mais fáceis...</p>
                    </div>
                    <div class="post-item">
                        <h3>Encontrando Posições Confortáveis para Dormir</h3>
                        <p class="post-meta">Postado por Maria • 1 dia atrás</p>
                        <p>Finalmente encontrei algumas posições que me ajudam a dormir a noite toda no meu terceiro trimestre...</p>
                    </div>
                </div>
            </section>
        </div>
    </section>

    <div class="section-divider" role="separator"></div>

    <section id="planning-tools" class="two-column-layout">
        <h2>Ferramentas Úteis de Planejamento da Gravidez</h2>
        <div class="content-wrapper">
            <div class="due-date-calculator content-box">
                <h3>Sua Calculadora de Data Prevista</h3>
                <form class="calculator-form" method="post" action="#">
                    <div class="form-group">
                        <label for="last-period">Quando foi o início da sua última menstruação?</label>
                        <input type="date" id="last-period" name="last-period" required
                            max="2023-12-31"
                            aria-describedby="date-help">
                        <small id="date-help" class="form-text">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Por favor, escolha uma data no passado</small>
                    </div>
                    <div class="form-group">
                        <label for="cycle-length">Qual é a duração do seu ciclo típico?</label>
                        <select id="cycle-length" name="cycle-length">
                            <option value="28">28 dias</option>
                            <option value="29">29 dias</option>
                            <option value="30">30 dias</option>
                            <option value="31">31 dias</option>
                        </select>
                    </div>
                    <button type="submit" name="calculate" class="calculate-btn">Calcular Minha Data Prevista</button>
                    <div class="result-box">
                        <p>Data estimada de chegada do seu bebê:
                            <span id="due-date">
                                Por favor, insira suas informações acima
                            </span>
                        </p>
                    </div>
                </form>
            </div>

            <div class="baby-names content-box">
                <h3 class="main-heading">Encontre o Nome Perfeito para o Bebê</h3>
                <p class="subtitle">Por favor, use as opções abaixo para explorar nossa coleção de belos nomes de bebê.</p>
                <form class="baby-names-form" method="post" action="">
                    <div class="form-group">
                        <label for="baby-name">Procurando por um nome específico?</label>
                        <input type="text" id="baby-name" name="name" placeholder="Digite um nome que você gosta...">
                    </div>
                    <div class="form-group">
                        <label for="name-gender">Gênero preferido:</label>
                        <select id="name-gender" name="gender">
                            <option value="all" selected>Todos os Gêneros</option>
                            <option value="girl">Menina</option>
                            <option value="boy">Menino</option>
                            <option value="neutral">Neutro</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="name-meaning">Significado especial:</label>
                        <input type="text" id="name-meaning" name="meaning" placeholder="Como 'força' ou 'esperança'...">
                    </div>
                    <button type="submit" name="search-names" class="submit-name-btn">Descobrir Nomes</button>

                    <div class="result-box">
                        <p>Preencha qualquer um dos campos acima e clique em "Descobrir Nomes" para encontrar o nome perfeito para seu bebê.</p>
                    </div>
                </form>

                <div class="names-list">
                    <h4>Resultados da Pesquisa</h4>

                    <?php if ($searchPerformed): ?>
                        <?php if (empty($searchResults)): ?>
                            <div id="no-results" style="text-align: center; padding: 20px; color: #555;">
                                Não conseguimos encontrar nomes que correspondam aos seus critérios. Tente ajustar sua pesquisa.
                            </div>
                        <?php else: ?>
                            <?php foreach ($searchResults as $name): ?>
                                <div class="name-card">
                                    <div class="name-header">
                                        <h4><?php echo htmlspecialchars($name['name']); ?></h4>
                                        <p class="gender"><?php echo htmlspecialchars($name['gender']); ?></p>
                                    </div>
                                    <p>Origem: <?php echo htmlspecialchars($name['origin']); ?></p>
                                    <p>Significado: <?php echo htmlspecialchars($name['meaning']); ?></p>
                                    <div class="name-actions">
                                        <a href="pages/baby.php?action=add&name=<?php echo urlencode($name['name']); ?>&gender=<?php echo urlencode($name['gender']); ?>&origin=<?php echo urlencode($name['origin']); ?>&meaning=<?php echo urlencode($name['meaning']); ?>" class="choose-name-btn">Salvar nos Meus Favoritos</a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    <?php else: ?>
                        <!-- Default baby name examples -->
                        <div class="name-card">
                            <div class="name-header">
                                <h4>Emma</h4>
                                <p class="gender">menina</p>
                            </div>
                            <p>Origem: alemã</p>
                            <p>Significado: Universal</p>
                            <div class="name-actions">
                                <a href="pages/baby.php?action=add&name=Emma&gender=girl&origin=german&meaning=Universal" class="choose-name-btn">Salvar nos Meus Favoritos</a>
                            </div>
                        </div>
                        <div class="name-card">
                            <div class="name-header">
                                <h4>Liam</h4>
                                <p class="gender">menino</p>
                            </div>
                            <p>Origem: irlandesa</p>
                            <p>Significado: Guerreiro determinado</p>
                            <div class="name-actions">
                                <a href="pages/baby.php?action=add&name=Liam&gender=boy&origin=irish&meaning=Strong-willed warrior" class="choose-name-btn">Salvar nos Meus Favoritos</a>
                            </div>
                        </div>
                        <div class="name-card">
                            <div class="name-header">
                                <h4>Sophia</h4>
                                <p class="gender">menina</p>
                            </div>
                            <p>Origem: grega</p>
                            <p>Significado: Sabedoria</p>
                            <div class="name-actions">
                                <a href="pages/baby.php?action=add&name=Sophia&gender=girl&origin=greek&meaning=Wisdom" class="choose-name-btn">Salvar nos Meus Favoritos</a>
                            </div>
                        </div>
                        <div class="name-card">
                            <div class="name-header">
                                <h4>Noah</h4>
                                <p class="gender">menino</p>
                            </div>
                            <p>Origem: inglesa</p>
                            <p>Significado: Descanso, conforto</p>
                            <div class="name-actions">
                                <a href="pages/baby.php?action=add&name=Noah&gender=boy&origin=english&meaning=Rest, comfort" class="choose-name-btn">Salvar nos Meus Favoritos</a>
                            </div>
                        </div>
                        <div class="name-card">
                            <div class="name-header">
                                <h4>Olivia</h4>
                                <p class="gender">menina</p>
                            </div>
                            <p>Origem: latina</p>
                            <p>Significado: Oliveira</p>
                            <div class="name-actions">
                                <a href="pages/baby.php?action=add&name=Olivia&gender=girl&origin=latin&meaning=Olive tree" class="choose-name-btn">Salvar nos Meus Favoritos</a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
</main>

<?php include __DIR__ . '/includes/footer.php'; ?>