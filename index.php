<?php
    // Set page title
    $pageTitle  = 'Home';
    $activePage = 'home';

    // Include header
    include __DIR__ . '/includes/header.php';

    // Include database connection
    require_once 'includes/database.php';

    // Include baby functions
    require_once 'includes/baby_functions.php';

    // Populate baby names table if empty
    populateBabyNamesIfEmpty();

    // Process search form submission
    $searchResults   = [];
    $searchPerformed = false;

    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['search-names'])) {
        $searchPerformed = true;
        $name            = isset($_POST['name']) ? trim($_POST['name']) : '';
        $gender          = isset($_POST['gender']) ? trim($_POST['gender']) : 'all';
        $meaning         = isset($_POST['meaning']) ? trim($_POST['meaning']) : '';

        // Search for baby names
        $searchResults = searchBabyNames($name, $gender, null, $meaning);
    }
?>

<header class="hero-section">
    <div class="slideshow-container">
        <button class="slide-nav prev" aria-label="Previous slide">&#10094;</button>
        <button class="slide-nav next" aria-label="Next slide">&#10095;</button>

        <div class="slide fade" aria-label="Slide 1">
            <img src="https://images.unsplash.com/photo-1590649880765-91b1956b8276" alt="Mother holding her pregnant belly">
            <div class="slide-caption">
                <h2>Supporting Your Journey</h2>
                <p>Friendly guidance through every step of your pregnancy</p>
            </div>
        </div>

        <div class="slide fade" aria-label="Slide 2">
            <img src="https://images.unsplash.com/photo-1531983412531-1f49a365ffed" alt="Pregnant woman doing yoga">
            <div class="slide-caption">
                <h2>Healthy Pregnancy</h2>
                <p>Simple tips and personalized advice for your well-being</p>
            </div>
        </div>

        <div class="slide fade" aria-label="Slide 3">
            <img src="https://images.unsplash.com/photo-**********-78848d9293e8" alt="Group of expecting mothers">
            <div class="slide-caption">
                <h2>Community Support</h2>
                <p>Share and connect with other parents on the same journey</p>
            </div>
        </div>

        <div class="slide-dots" role="tablist">
            <button class="dot" role="tab" aria-label="Slide 1"></button>
            <button class="dot" role="tab" aria-label="Slide 2"></button>
            <button class="dot" role="tab" aria-label="Slide 3"></button>
        </div>
    </div>
</header>

<main>
    <section id="faq-community" class="two-column-layout">
        <div class="content-wrapper">
            <section class="faq-section content-box">
                <h2>Your Pregnancy Questions Answered</h2>
                <div class="faq-content">
                    <div class="faq-item">
                        <h3>When should I first see a doctor?</h3>
                        <p>It's best to schedule your first prenatal visit as soon as you discover you're pregnant, ideally within the first 8 weeks. This early care helps ensure a healthy start for you and your baby.</p>
                    </div>
                    <div class="faq-item">
                        <h3>What foods should I avoid?</h3>
                        <p>For your safety and your baby's health, avoid raw fish (like sushi), unpasteurized dairy products, raw or undercooked eggs, and high-mercury fish such as shark, swordfish, and mackerel during pregnancy.</p>
                    </div>
                    <div class="faq-item">
                        <h3>How much weight gain is normal?</h3>
                        <p>Healthy weight gain during pregnancy varies from person to person. For those with a normal BMI before pregnancy, a gain of 25-35 pounds is typically recommended, but your healthcare provider can give you personalized guidance.</p>
                    </div>
                    <div class="faq-item">
                        <h3>When will I feel my baby move?</h3>
                        <p>The exciting moment of feeling your baby's first movements (called "quickening") usually happens between 18-20 weeks for first-time mothers. If you've been pregnant before, you might notice these gentle flutters a bit earlier, around 16-18 weeks.</p>
                    </div>
                </div>
            </section>

            <section class="community-section content-box">
                <h2>Recent Community Posts</h2>
                <div class="community-content">
                    <div class="post-item">
                        <h3>Morning Sickness Tips</h3>
                        <p class="post-meta">Posted by Sarah • 2 hours ago</p>
                        <p>Found that ginger tea really helps with morning sickness...</p>
                    </div>
                    <div class="post-item">
                        <h3>Safe Third Trimester Exercises</h3>
                        <p class="post-meta">Posted by Emma • 5 hours ago</p>
                        <p>Looking for gentle exercises that are safe in my final trimester. Any suggestions?</p>
                    </div>
                    <div class="post-item">
                        <h3>My Baby Registry Essentials</h3>
                        <p class="post-meta">Posted by Lisa • 1 day ago</p>
                        <p>I've put together a list of must-have items that made our first weeks with baby so much easier...</p>
                    </div>
                    <div class="post-item">
                        <h3>Finding Comfortable Sleep Positions</h3>
                        <p class="post-meta">Posted by Maria • 1 day ago</p>
                        <p>Finally found some positions that help me sleep through the night in my third trimester...</p>
                    </div>
                </div>
            </section>
        </div>
    </section>

    <div class="section-divider" role="separator"></div>

    <section id="planning-tools" class="two-column-layout">
        <h2>Helpful Pregnancy Planning Tools</h2>
        <div class="content-wrapper">
            <div class="due-date-calculator content-box">
                <h3>Your Due Date Calculator</h3>
                <form class="calculator-form" method="post" action="#">
                    <div class="form-group">
                        <label for="last-period">When did your last period start?</label>
                        <input type="date" id="last-period" name="last-period" required
                            max="2023-12-31"
                            aria-describedby="date-help">
                        <small id="date-help" class="form-text">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Please choose a date in the past</small>
                    </div>
                    <div class="form-group">
                        <label for="cycle-length">How long is your typical cycle?</label>
                        <select id="cycle-length" name="cycle-length">
                            <option value="28">28 days</option>
                            <option value="29">29 days</option>
                            <option value="30">30 days</option>
                            <option value="31">31 days</option>
                        </select>
                    </div>
                    <button type="submit" name="calculate" class="calculate-btn">Find My Due Date</button>
                    <div class="result-box">
                        <p>Your baby's estimated arrival date:
                            <span id="due-date">
                                Please enter your information above
                            </span>
                        </p>
                    </div>
                </form>
            </div>

            <div class="baby-names content-box">
                <h3 class="main-heading">Find the Perfect Baby Name</h3>
                <p class="subtitle">Please use the options below to explore our collection of beautiful baby names.</p>
                <form class="baby-names-form" method="post" action="">
                    <div class="form-group">
                        <label for="baby-name">Looking for a specific name?</label>
                        <input type="text" id="baby-name" name="name" placeholder="Type a name you like...">
                    </div>
                    <div class="form-group">
                        <label for="name-gender">Preferred gender:</label>
                        <select id="name-gender" name="gender">
                            <option value="all" selected>All Genders</option>
                            <option value="girl">Girl</option>
                            <option value="boy">Boy</option>
                            <option value="neutral">Neutral</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="name-meaning">Special meaning:</label>
                        <input type="text" id="name-meaning" name="meaning" placeholder="Like 'strength' or 'hope'...">
                    </div>
                    <button type="submit" name="search-names" class="submit-name-btn">Discover Names</button>

                    <div class="result-box">
                        <p>Fill in any of the fields above and click "Discover Names" to find your perfect baby name.</p>
                    </div>
                </form>

                <div class="names-list">
                    <h4>Search Results</h4>

                    <?php if ($searchPerformed): ?>
<?php if (empty($searchResults)): ?>
                            <div id="no-results" style="text-align: center; padding: 20px; color: #555;">
                                We couldn't find any names matching your criteria. Try adjusting your search.
                            </div>
                        <?php else: ?>
<?php foreach ($searchResults as $name): ?>
                                <div class="name-card">
                                    <div class="name-header">
                                        <h4><?php echo htmlspecialchars($name['name']); ?></h4>
                                        <p class="gender"><?php echo htmlspecialchars($name['gender']); ?></p>
                                    </div>
                                    <p>Origin:                                               <?php echo htmlspecialchars($name['origin']); ?></p>
                                    <p>Meaning:                                                <?php echo htmlspecialchars($name['meaning']); ?></p>
                                    <div class="name-actions">
                                        <a href="pages/baby.php?action=add&name=<?php echo urlencode($name['name']); ?>&gender=<?php echo urlencode($name['gender']); ?>&origin=<?php echo urlencode($name['origin']); ?>&meaning=<?php echo urlencode($name['meaning']); ?>" class="choose-name-btn">Save to My Favorites</a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
<?php endif; ?>
<?php else: ?>
                        <!-- Default baby name examples -->
                        <div class="name-card">
                            <div class="name-header">
                                <h4>Emma</h4>
                                <p class="gender">girl</p>
                            </div>
                            <p>Origin: german</p>
                            <p>Meaning: Universal</p>
                            <div class="name-actions">
                                <a href="pages/baby.php?action=add&name=Emma&gender=girl&origin=german&meaning=Universal" class="choose-name-btn">Save to My Favorites</a>
                            </div>
                        </div>
                        <div class="name-card">
                            <div class="name-header">
                                <h4>Liam</h4>
                                <p class="gender">boy</p>
                            </div>
                            <p>Origin: irish</p>
                            <p>Meaning: Strong-willed warrior</p>
                            <div class="name-actions">
                                <a href="pages/baby.php?action=add&name=Liam&gender=boy&origin=irish&meaning=Strong-willed warrior" class="choose-name-btn">Save to My Favorites</a>
                            </div>
                        </div>
                        <div class="name-card">
                            <div class="name-header">
                                <h4>Sophia</h4>
                                <p class="gender">girl</p>
                            </div>
                            <p>Origin: greek</p>
                            <p>Meaning: Wisdom</p>
                            <div class="name-actions">
                                <a href="pages/baby.php?action=add&name=Sophia&gender=girl&origin=greek&meaning=Wisdom" class="choose-name-btn">Save to My Favorites</a>
                            </div>
                        </div>
                        <div class="name-card">
                            <div class="name-header">
                                <h4>Noah</h4>
                                <p class="gender">boy</p>
                            </div>
                            <p>Origin: english</p>
                            <p>Meaning: Rest, comfort</p>
                            <div class="name-actions">
                                <a href="pages/baby.php?action=add&name=Noah&gender=boy&origin=english&meaning=Rest, comfort" class="choose-name-btn">Save to My Favorites</a>
                            </div>
                        </div>
                        <div class="name-card">
                            <div class="name-header">
                                <h4>Olivia</h4>
                                <p class="gender">girl</p>
                            </div>
                            <p>Origin: latin</p>
                            <p>Meaning: Olive tree</p>
                            <div class="name-actions">
                                <a href="pages/baby.php?action=add&name=Olivia&gender=girl&origin=latin&meaning=Olive tree" class="choose-name-btn">Save to My Favorites</a>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
</main>

<?php include __DIR__ . '/includes/footer.php'; ?>