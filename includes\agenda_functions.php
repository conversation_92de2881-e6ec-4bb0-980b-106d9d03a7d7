<?php
/**
 * Functions for agenda management
 */

// Include database connection
require_once 'database.php';

/**
 * Get all agenda items for a user
 *
 * @param int $userId User ID
 * @return array Array of agenda items
 */
function getAgendaItems($userId)
{
    global $connection;

    $sql = "SELECT a.*, b.name as baby_name
            FROM Agenda a
            LEFT JOIN Baby b ON a.baby_id = b.id
            WHERE a.user_id = ?
            ORDER BY a.date ASC, a.time ASC";

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    $items = [];
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $items[] = $row;
        }
    }

    return $items;
}

/**
 * Get agenda items for a specific date
 *
 * @param int $userId User ID
 * @param string $date Date in Y-m-d format
 * @return array Array of agenda items for the date
 */
function getAgendaItemsByDate($userId, $date)
{
    global $connection;

    $sql = "SELECT a.*, b.name as baby_name
            FROM Agenda a
            LEFT JOIN Baby b ON a.baby_id = b.id
            WHERE a.user_id = ? AND a.date = ?
            ORDER BY a.time ASC";

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("is", $userId, $date);
    $stmt->execute();
    $result = $stmt->get_result();

    $items = [];
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $items[] = $row;
        }
    }

    return $items;
}

/**
 * Get a single agenda item by ID
 *
 * @param int $itemId Agenda item ID
 * @param int $userId User ID (for security)
 * @return array|null Agenda item data or null if not found
 */
function getAgendaItem($itemId, $userId)
{
    global $connection;

    $sql = "SELECT a.*, b.name as baby_name
            FROM Agenda a
            LEFT JOIN Baby b ON a.baby_id = b.id
            WHERE a.id = ? AND a.user_id = ?";

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("ii", $itemId, $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return null;
}

/**
 * Add a new agenda item
 *
 * @param int $userId User ID
 * @param string $title Item title
 * @param string $description Item description
 * @param string $date Date in Y-m-d format
 * @param string $time Time in H:i format
 * @param int|null $babyId Baby ID (optional)
 * @return int|false New item ID if successful, false otherwise
 */
function addAgendaItem($userId, $title, $description, $date, $time, $babyId = null)
{
    global $connection;

    $sql = "INSERT INTO Agenda (user_id, title, description, date, time, baby_id)
            VALUES (?, ?, ?, ?, ?, ?)";

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("issssi", $userId, $title, $description, $date, $time, $babyId);

    if ($stmt->execute()) {
        return $connection->insert_id;
    }

    return false;
}

/**
 * Update an existing agenda item
 *
 * @param int $itemId Agenda item ID
 * @param int $userId User ID (for security)
 * @param string $title Item title
 * @param string $description Item description
 * @param string $date Date in Y-m-d format
 * @param string $time Time in H:i format
 * @param int|null $babyId Baby ID (optional)
 * @return bool True if successful, false otherwise
 */
function updateAgendaItem($itemId, $userId, $title, $description, $date, $time, $babyId = null)
{
    global $connection;

    $sql = "UPDATE Agenda
            SET title = ?, description = ?, date = ?, time = ?, baby_id = ?
            WHERE id = ? AND user_id = ?";

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("ssssiis", $title, $description, $date, $time, $babyId, $itemId, $userId);

    return $stmt->execute() && $stmt->affected_rows > 0;
}

/**
 * Delete an agenda item
 *
 * @param int $itemId Agenda item ID
 * @param int $userId User ID (for security)
 * @return bool True if successful, false otherwise
 */
function deleteAgendaItem($itemId, $userId)
{
    global $connection;

    $sql = "DELETE FROM Agenda WHERE id = ? AND user_id = ?";

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("ii", $itemId, $userId);

    return $stmt->execute() && $stmt->affected_rows > 0;
}

/**
 * Get all babies for a user (for dropdown selection)
 *
 * @param int $userId User ID
 * @return array Array of babies
 */
function getUserBabies($userId)
{
    global $connection;

    $sql = "SELECT id, name FROM Baby WHERE user_id = ? ORDER BY name ASC";

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    $babies = [];
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $babies[] = $row;
        }
    }

    return $babies;
}

/**
 * Get upcoming agenda items for a user (for dashboard)
 *
 * @param int $userId User ID
 * @param int $limit Number of items to return
 * @return array Array of upcoming agenda items
 */
function getUpcomingAgendaItems($userId, $limit = 5)
{
    global $connection;

    $today = date('Y-m-d');

    $sql = "SELECT a.*, b.name as baby_name
            FROM Agenda a
            LEFT JOIN Baby b ON a.baby_id = b.id
            WHERE a.user_id = ? AND (a.date > ? OR (a.date = ? AND a.time >= ?))
            ORDER BY a.date ASC, a.time ASC
            LIMIT ?";

    $currentTime = date('H:i:s');

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("isssi", $userId, $today, $today, $currentTime, $limit);
    $stmt->execute();
    $result = $stmt->get_result();

    $items = [];
    if ($result) {
        while ($row = $result->fetch_assoc()) {
            $items[] = $row;
        }
    }

    return $items;
}
