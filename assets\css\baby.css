/* View Toggle Styles */
.view-toggle {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.view-toggle button {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 8px 15px;
  cursor: pointer !important;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
  font-size: 14px;
}

.view-toggle button.active {
  background-color: #b2d8b2 !important;
  color: white !important;
  border-color: #b2d8b2 !important;
}

.view-toggle button:hover:not(.active) {
  background-color: #e5e5e5 !important;
}

.view-toggle button i {
  font-size: 12px;
}

/* Grid View Styles */
.babies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

/* List View Styles */
.babies-grid.list-view {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.babies-grid.list-view .baby-card {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.babies-grid.list-view .baby-card h3 {
  flex: 0 0 200px;
  margin-bottom: 0;
}

.babies-grid.list-view .baby-details {
  flex: 1;
  flex-direction: row;
  flex-wrap: wrap;
  margin: 0 20px;
}

.babies-grid.list-view .baby-details p {
  margin-right: 20px;
  margin-bottom: 0;
}

.babies-grid.list-view .baby-actions {
  margin-top: 0;
}

.baby-card {
  background-color: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.baby-card h3 {
  color: #6b8e23; /* Keeping this dark green for headings as per style guide */
  margin-bottom: 15px;
}

.baby-details {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.baby-details p {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #666;
}

.baby-actions {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* Edit and Delete button styles */
.edit-btn,
.delete-btn {
  background-color: #b2d8b2;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 8px 15px;
  font-size: 14px;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.3s ease;
}

.edit-btn:hover,
.delete-btn:hover {
  background-color: #9ac89a;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.delete-btn {
  background-color: #b2d8b2; /* Same color as other buttons */
}

/* Empty grid styles */
.babies-grid:empty {
  min-height: 100px;
}

/* Baby Header Styles */
.baby-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.baby-header h2 {
  margin: 0;
  color: #6b8e23; /* Keeping this dark green for headings as per style guide */
}

/* Add Baby Button Styles */
.add-baby-btn {
  background-color: #b2d8b2;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px 15px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
}

.add-baby-btn:hover {
  background-color: #9ac89a;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.add-baby-btn i {
  font-size: 12px;
}

/* Responsive Design for Baby Page */
@media (max-width: 768px) {
  .baby-container {
    padding: 1rem 0.5rem;
  }

  .baby-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .baby-header h2 {
    font-size: 1.5rem;
  }

  .add-baby-btn {
    width: 100%;
    justify-content: center;
    padding: 12px 15px;
    font-size: 16px;
  }

  .baby-form-container {
    padding: 1rem;
    margin: 0 0.5rem;
  }

  .baby-form {
    gap: 1rem;
  }

  .form-group {
    margin-bottom: 1.2rem;
  }

  .form-group label {
    font-size: 0.95rem;
    margin-bottom: 0.5rem;
  }

  input,
  select,
  textarea {
    padding: 12px;
    font-size: 16px; /* Prevents zoom on iOS */
    border-radius: 8px;
  }

  .submit-btn {
    width: 100%;
    padding: 12px;
    font-size: 16px;
    margin-top: 1rem;
  }

  .baby-list {
    gap: 1rem;
  }

  .baby-card {
    padding: 1rem;
    margin: 0 0.5rem 1rem 0.5rem;
  }

  .baby-card h3 {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
  }

  .baby-info {
    font-size: 0.9rem;
  }

  .baby-actions {
    flex-direction: column;
    gap: 0.5rem;
    margin-top: 1rem;
  }

  .baby-actions button,
  .baby-actions a {
    width: 100%;
    text-align: center;
    padding: 8px 12px;
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .baby-container {
    padding: 0.8rem 0.3rem;
  }

  .baby-header h2 {
    font-size: 1.4rem;
  }

  .add-baby-btn {
    padding: 10px 12px;
    font-size: 14px;
  }

  .baby-form-container {
    padding: 0.8rem;
    margin: 0 0.2rem;
  }

  .baby-card {
    padding: 0.8rem;
    margin: 0 0.2rem 0.8rem 0.2rem;
  }

  .baby-card h3 {
    font-size: 1.1rem;
  }

  .baby-info {
    font-size: 0.85rem;
  }

  .baby-actions button,
  .baby-actions a {
    padding: 6px 10px;
    font-size: 13px;
  }
}

/* Baby Form Container Styles */
.baby-form-container {
  background-color: white;
  border-radius: 10px;
  padding: 0 25px 25px 25px; /* Removed top padding to let the header handle it */
  margin-bottom: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10; /* Ensure it appears above other elements */
  width: 100%;
  max-width: 100%;
  height: 60vh; /* Height as percentage of viewport height */
  max-height: 600px; /* Maximum height cap */
  overflow-y: auto; /* Enable vertical scrolling if content exceeds max-height */
  box-sizing: border-box;
  display: none; /* Hidden by default, shown via JavaScript */
  scrollbar-width: thin; /* For Firefox */
  scrollbar-color: #b2d8b2 #f0f0f0; /* For Firefox */
}

/* Scrollbar styles for WebKit browsers (Chrome, Safari, Edge) */
.baby-form-container::-webkit-scrollbar {
  width: 8px;
}

.baby-form-container::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 4px;
}

.baby-form-container::-webkit-scrollbar-thumb {
  background-color: #b2d8b2;
  border-radius: 4px;
}

.baby-form-container::-webkit-scrollbar-thumb:hover {
  background-color: #9ac89a;
}

/* Form header with proper spacing */
.baby-form-header {
  position: relative; /* Not sticky anymore */
  background-color: white;
  padding: 20px 0 15px 0; /* Added top padding */
  margin-bottom: 15px;
  z-index: 2;
  border-bottom: 1px solid #f0f0f0;
}

/* Close form button styles */
.close-form-btn {
  transition: all 0.3s ease;
}

.close-form-btn:hover {
  background-color: #9ac89a !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.baby-form-container h3 {
  color: #6b8e23; /* Keeping this dark green for headings as per style guide */
  margin-top: 0;
  margin-bottom: 20px;
}

/* Form content container to better utilize space */
.baby-form-content {
  padding: 10px 0;
  display: flex;
  flex-direction: column;
  height: calc(100% - 90px); /* Adjusted for larger header height */
}

.form-group {
  margin-bottom: 20px; /* Moderate spacing between form groups */
}

.form-group label {
  display: block;
  margin-bottom: 8px; /* Increased spacing below labels */
  color: #555;
  font-size: 16px; /* Larger font size for labels */
  font-weight: 500; /* Slightly bolder labels */
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 12px; /* Increased padding for inputs */
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 15px; /* Larger font size for inputs */
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  border-color: #b2d8b2;
  outline: none;
  box-shadow: 0 0 0 2px rgba(178, 216, 178, 0.2);
}

.form-actions {
  margin-top: auto; /* Push to bottom of flex container */
  padding-top: 25px;
  display: flex;
  gap: 15px; /* Increased gap between buttons */
  border-top: 1px solid #f0f0f0;
}

.submit-btn {
  background-color: #b2d8b2;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 12px 25px; /* Larger padding */
  font-size: 16px; /* Larger font size */
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1; /* Take up available space */
  text-align: center;
}

.submit-btn:hover {
  background-color: #9ac89a;
  transform: translateY(-2px); /* Slight lift effect on hover */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 12px 25px; /* Larger padding */
  font-size: 16px; /* Larger font size */
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1; /* Take up available space */
  text-align: center;
}

.cancel-btn:hover {
  background-color: #e5e5e5;
  transform: translateY(-2px); /* Slight lift effect on hover */
}

/* Top-right cancel button styles */
button.close,
.btn-close,
.modal-header .close,
.modal-header .btn-close,
button[class*="cancel"],
a[class*="cancel"] {
  cursor: pointer !important;
}

/* Green cancel button styles */
.btn-success.cancel,
button.btn-success[class*="cancel"],
a.btn-success[class*="cancel"] {
  background-color: #b2d8b2 !important;
  border-color: #b2d8b2 !important;
  color: white !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
}

.btn-success.cancel:hover,
button.btn-success[class*="cancel"]:hover,
a.btn-success[class*="cancel"]:hover {
  background-color: #9ac89a !important;
  border-color: #9ac89a !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

/* Login prompt button */
.login-prompt-btn {
  background-color: #b2d8b2;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  transition: all 0.3s ease;
}

.login-prompt-btn:hover {
  background-color: #9ac89a;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}
