<?php
// Include database connection
require_once 'includes/database.php';

// Check Pregnancy table
echo "<h2>Checking Pregnancy Table</h2>";

// Check if table exists
$tableResult = $connection->query("SHOW TABLES LIKE 'Pregnancy'");
if (! $tableResult || $tableResult->num_rows === 0) {
    echo "<p>Pregnancy table does not exist!</p>";
} else {
    echo "<p>Pregnancy table exists.</p>";

    // Get all pregnancies
    $sql    = "SELECT * FROM Pregnancy";
    $result = $connection->query($sql);

    if (! $result) {
        echo "<p>Error querying Pregnancy table: " . $connection->error . "</p>";
    } else if ($result->num_rows === 0) {
        echo "<p>No pregnancies found in the database.</p>";
    } else {
        echo "<p>Found " . $result->num_rows . " pregnancies:</p>";
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>User ID</th><th>Start Date</th><th>Due Date</th><th>Notes</th><th>Title</th></tr>";

        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['user_id'] . "</td>";
            echo "<td>" . $row['start_date'] . "</td>";
            echo "<td>" . $row['due_date'] . "</td>";
            echo "<td>" . $row['notes'] . "</td>";
            echo "<td>" . (isset($row['title']) ? $row['title'] : 'N/A') . "</td>";
            echo "</tr>";
        }

        echo "</table>";
    }
}

// Check Baby table
echo "<h2>Checking Baby Table</h2>";

// Check if table exists
$tableResult = $connection->query("SHOW TABLES LIKE 'Baby'");
if (! $tableResult || $tableResult->num_rows === 0) {
    echo "<p>Baby table does not exist!</p>";
} else {
    echo "<p>Baby table exists.</p>";

    // Get table structure
    $sql    = "DESCRIBE Baby";
    $result = $connection->query($sql);

    if (! $result) {
        echo "<p>Error describing Baby table: " . $connection->error . "</p>";
    } else {
        echo "<p>Baby table structure:</p>";
        echo "<table border='1'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "<td>" . $row['Default'] . "</td>";
            echo "<td>" . $row['Extra'] . "</td>";
            echo "</tr>";
        }

        echo "</table>";
    }
}

// Check foreign key constraints
echo "<h2>Checking Foreign Key Constraints</h2>";

$sql = "SELECT * FROM information_schema.KEY_COLUMN_USAGE
        WHERE REFERENCED_TABLE_NAME = 'Pregnancy'
        AND TABLE_NAME = 'Baby'";
$result = $connection->query($sql);

if (! $result) {
    echo "<p>Error checking foreign key constraints: " . $connection->error . "</p>";
} else if ($result->num_rows === 0) {
    echo "<p>No foreign key constraints found between Baby and Pregnancy tables.</p>";
} else {
    echo "<p>Found " . $result->num_rows . " foreign key constraints:</p>";
    echo "<table border='1'>";
    echo "<tr><th>Constraint Name</th><th>Table</th><th>Column</th><th>Referenced Table</th><th>Referenced Column</th></tr>";

    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['CONSTRAINT_NAME'] . "</td>";
        echo "<td>" . $row['TABLE_NAME'] . "</td>";
        echo "<td>" . $row['COLUMN_NAME'] . "</td>";
        echo "<td>" . $row['REFERENCED_TABLE_NAME'] . "</td>";
        echo "<td>" . $row['REFERENCED_COLUMN_NAME'] . "</td>";
        echo "</tr>";
    }

    echo "</table>";
}
