<?php
// Include authentication check
require_once __DIR__ . '/../includes/auth_check.php';

// Include the baby functions
require_once __DIR__ . '/../includes/baby_functions.php';

// Set page title and active page
$pageTitle  = 'Informações do Bebê';
$activePage = 'baby';

// Include header
include __DIR__ . '/../includes/header.php';
?>

<!-- All JavaScript functionality is now in baby.js -->

<?php
// Set a default user ID for demonstration
$userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 4; // This would normally come from the logged-in user's session

// Get user's babies
$babies = getUserBabies($userId);

// Get user's pregnancies for the add/edit form
$pregnancies = getUserPregnancies($userId);

// Get all available baby genders
$babyGenders = getAllBabyGenders();

// Initialize variables
$editBaby    = null;
$message     = '';
$messageType = '';

// Check if we're adding a baby from the baby names search
$nameFromSearch = null;
if (isset($_GET['action']) && $_GET['action'] === 'add' && isset($_GET['name'])) {
    $nameFromSearch = [
        'name'    => $_GET['name'],
        'gender'  => isset($_GET['gender']) ? $_GET['gender'] : 'unknown',
        'origin'  => isset($_GET['origin']) ? $_GET['origin'] : '',
        'meaning' => isset($_GET['meaning']) ? $_GET['meaning'] : '',
    ];
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        // Add new baby
        if ($_POST['action'] === 'add') {
            $name        = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_SPECIAL_CHARS);
            $genderId    = filter_input(INPUT_POST, 'gender_id', FILTER_VALIDATE_INT);
            $birthDate   = filter_input(INPUT_POST, 'birth_date', FILTER_SANITIZE_SPECIAL_CHARS);
            $weight      = filter_input(INPUT_POST, 'weight', FILTER_VALIDATE_FLOAT);
            $height      = filter_input(INPUT_POST, 'height', FILTER_VALIDATE_FLOAT);
            $pregnancyId = filter_input(INPUT_POST, 'pregnancy_id', FILTER_VALIDATE_INT);

            if ($name && $genderId && $birthDate) {
                $result = createBaby($userId, $name, $genderId, $birthDate, $weight, $height, $pregnancyId);

                if ($result) {
                    $message     = "Bebê adicionado com sucesso!";
                    $messageType = "success";

                    // Refresh babies list
                    $babies = getUserBabies($userId);
                } else {
                    $message     = "Erro ao adicionar bebê.";
                    $messageType = "error";
                }
            } else {
                $message     = "Por favor, preencha todos os campos obrigatórios.";
                $messageType = "error";
            }
        }

        // Edit baby
        if ($_POST['action'] === 'edit' && isset($_POST['baby_id'])) {
            $babyId      = filter_input(INPUT_POST, 'baby_id', FILTER_VALIDATE_INT);
            $name        = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_SPECIAL_CHARS);
            $genderId    = filter_input(INPUT_POST, 'gender_id', FILTER_VALIDATE_INT);
            $birthDate   = filter_input(INPUT_POST, 'birth_date', FILTER_SANITIZE_SPECIAL_CHARS);
            $weight      = filter_input(INPUT_POST, 'weight', FILTER_VALIDATE_FLOAT);
            $height      = filter_input(INPUT_POST, 'height', FILTER_VALIDATE_FLOAT);
            $pregnancyId = filter_input(INPUT_POST, 'pregnancy_id', FILTER_VALIDATE_INT);

            if ($babyId && $name && $genderId && $birthDate) {
                $result = updateBaby($babyId, $name, $genderId, $birthDate, $weight, $height, $pregnancyId);

                if ($result) {
                    $message     = "Bebê atualizado com sucesso!";
                    $messageType = "success";

                    // Refresh babies list
                    $babies = getUserBabies($userId);
                } else {
                    $message     = "Erro ao atualizar bebê.";
                    $messageType = "error";
                }
            } else {
                $message     = "Por favor, preencha todos os campos obrigatórios.";
                $messageType = "error";
            }
        }

        // Delete baby
        if ($_POST['action'] === 'delete' && isset($_POST['baby_id'])) {
            $babyId = filter_input(INPUT_POST, 'baby_id', FILTER_VALIDATE_INT);

            if ($babyId && deleteBaby($babyId)) {
                $message     = "Bebê excluído com sucesso!";
                $messageType = "success";

                // Refresh babies list
                $babies = getUserBabies($userId);
            } else {
                $message     = "Erro ao excluir bebê.";
                $messageType = "error";
            }
        }
    }
}

// Check if we're editing a baby
if (isset($_GET['action']) && $_GET['action'] === 'edit' && isset($_GET['id'])) {
    $babyId = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
    if ($babyId) {
        $editBaby = getBabyById($babyId);
    }
}
?>

<main>
    <?php if (! empty($message)): ?>
        <div class="message<?php echo $messageType; ?>">
            <?php echo $message; ?>
        </div>
    <?php endif; ?>

    <?php if ($isLoggedIn): ?>
        <section class="baby-container">
            <div class="baby-header">
                <h2>Seus Bebês</h2>
                <button type="button" class="add-baby-btn" onclick="showBabyForm(); return false;" style="cursor: pointer;"><i class="fas fa-plus"></i> Adicionar Novo Bebê</button>
            </div>

            <!-- Baby form container with internal scrolling -->
            <div class="baby-form-container">
                <div class="baby-form-header" style="display: flex; justify-content: space-between; align-items: center; padding: 25px 0;">
                    <h3 style="margin: 0; font-size: 20px;"><?php echo $editBaby ? 'Editar Bebê' : 'Adicionar Novo Bebê'; ?></h3>
                    <button type="button" class="close-form-btn" onclick="hideBabyForm(); return false;" style="background: #b2d8b2; border: none; border-radius: 5px; color: white; font-size: 16px; padding: 5px 10px; cursor: pointer; transition: all 0.3s ease;">Fechar</button>
                </div>
                <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                    <div class="baby-form-content">
                        <input type="hidden" name="action" value="<?php echo $editBaby ? 'edit' : 'add'; ?>">
                        <?php if ($editBaby): ?>
                            <input type="hidden" name="baby_id" value="<?php echo $editBaby['id']; ?>">
                        <?php endif; ?>

                        <div class="form-group">
                            <label for="name">Nome:</label>
                            <input type="text" id="name" name="name" value="<?php echo $editBaby ? htmlspecialchars($editBaby['name']) : ($nameFromSearch ? htmlspecialchars($nameFromSearch['name']) : ''); ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="gender_id">Gênero:</label>
                            <select id="gender_id" name="gender_id" required>
                                <option value="">Selecionar Gênero</option>
                                <?php foreach ($babyGenders as $gender): ?>
                                    <option value="<?php echo $gender['id']; ?>" <?php echo ($editBaby && $editBaby['gender_id'] == $gender['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($gender['gender_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="birth_date">Data de Nascimento:</label>
                            <input type="date" id="birth_date" name="birth_date" value="<?php echo $editBaby ? htmlspecialchars($editBaby['birth_date']) : ''; ?>" required>
                        </div>

                        <div class="form-group">
                            <label for="weight">Peso (kg):</label>
                            <input type="number" id="weight" name="weight" step="0.01" value="<?php echo $editBaby ? htmlspecialchars($editBaby['weight']) : ''; ?>">
                        </div>

                        <div class="form-group">
                            <label for="height">Altura (cm):</label>
                            <input type="number" id="height" name="height" step="0.1" value="<?php echo $editBaby ? htmlspecialchars($editBaby['height']) : ''; ?>">
                        </div>

                        <div class="form-group">
                            <label for="pregnancy_id">Gravidez Relacionada:</label>
                            <select id="pregnancy_id" name="pregnancy_id">
                                <option value="">Nenhuma</option>
                                <?php foreach ($pregnancies as $pregnancy): ?>
                                    <option value="<?php echo $pregnancy['id']; ?>" <?php echo ($editBaby && $editBaby['pregnancy_id'] == $pregnancy['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($pregnancy['title']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="submit-btn"><?php echo $editBaby ? 'Atualizar Bebê' : 'Adicionar Bebê'; ?></button>
                            <?php if ($editBaby): ?>
                                <a href="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" class="cancel-btn">Cancelar</a>
                            <?php else: ?>
                                <button type="button" class="cancel-btn" id="cancel-add-baby" onclick="hideBabyForm(); return false;" style="cursor: pointer;">Cancelar</button>
                            <?php endif; ?>
                        </div>
                    </div>
                </form>
            </div>

            <div class="view-toggle">
                <button type="button" class="grid-view-btn active" onclick="showGridView(); return false;"><i class="fas fa-th"></i> Visualização em Grade</button>
                <button type="button" class="list-view-btn" onclick="showListView(); return false;"><i class="fas fa-list"></i> Visualização em Lista</button>
            </div>

            <?php if (empty($babies)): ?>
                <div class="babies-grid">
                    <!-- Empty grid when no babies are added -->
                </div>
            <?php else: ?>
                <div class="babies-grid">
                    <?php foreach ($babies as $baby): ?>
                        <div class="baby-card">
                            <h3><?php echo htmlspecialchars($baby['name']); ?></h3>
                            <div class="baby-details">
                                <p><i class="fas fa-venus-mars"></i> <?php echo htmlspecialchars($baby['gender_name']); ?></p>
                                <p><i class="fas fa-birthday-cake"></i> <?php echo date('M j, Y', strtotime($baby['birth_date'])); ?></p>
                                <?php if (! empty($baby['weight'])): ?>
                                    <p><i class="fas fa-weight"></i><?php echo htmlspecialchars($baby['weight']); ?> kg</p>
                                <?php endif; ?>
                                <?php if (! empty($baby['height'])): ?>
                                    <p><i class="fas fa-ruler-vertical"></i><?php echo htmlspecialchars($baby['height']); ?> cm</p>
                                <?php endif; ?>
                            </div>
                            <div class="baby-actions">
                                <a href="?action=edit&id=<?php echo $baby['id']; ?>" class="edit-btn">Editar</a>
                                <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>"
                                    onsubmit="return confirm('Tem certeza de que deseja excluir este bebê?');"
                                    style="display: inline;">
                                    <input type="hidden" name="action" value="delete">
                                    <input type="hidden" name="baby_id" value="<?php echo $baby['id']; ?>">
                                    <button type="submit" class="delete-btn">Excluir</button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </section>
    <?php else: ?>
        <div class="agenda-container">
            <div class="login-prompt">
                <h2>Acesse as Informações do Seu Bebê</h2>
                <p>Por favor, faça login para visualizar e gerenciar as informações do seu bebê.</p>
                <a href="#" class="login-trigger login-prompt-btn">Entrar</a>
            </div>
        </div>
    <?php endif; ?>
</main>



<?php include __DIR__ . '/../includes/footer.php'; ?>
</div>

<script src="../assets/js/script.js"></script>
<script src="../assets/js/baby-simple.js"></script>