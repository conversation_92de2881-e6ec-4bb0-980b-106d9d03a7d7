<?php
    // Add this at the very top of the file
    error_log("Admin.php is being loaded - " . date('Y-m-d H:i:s'));

    // Include authentication check
    require_once __DIR__ . '/../includes/auth_check.php';

    // Include database connection
    require_once __DIR__ . '/../includes/database.php';

    // Check if user is an admin
    $isAdmin = false;

    if (isset($_SESSION['user_id'])) {
        $userId = $_SESSION['user_id'];

        // For simplicity, we'll consider user ID 1 as the admin
        if ($userId == 1) {
            $isAdmin = true;
        }

        // Also check the database for role if available
        try {
            // Check if role column exists
            $checkRoleColumnSql    = "SHOW COLUMNS FROM Users LIKE 'role'";
            $checkRoleColumnResult = $connection->query($checkRoleColumnSql);

            if ($checkRoleColumnResult && $checkRoleColumnResult->num_rows > 0) {
                // Role column exists, check if user is admin
                $checkAdminSql  = "SELECT role FROM Users WHERE id = ?";
                $checkAdminStmt = $connection->prepare($checkAdminSql);

                if ($checkAdminStmt) {
                    $checkAdminStmt->bind_param("i", $userId);
                    $checkAdminStmt->execute();
                    $checkAdminResult = $checkAdminStmt->get_result();

                    if ($checkAdminResult && $checkAdminResult->num_rows > 0) {
                        $userData = $checkAdminResult->fetch_assoc();
                        // If role is admin, set isAdmin to true
                        if ($userData['role'] === 'admin') {
                            $isAdmin = true;
                        }
                    }
                }
            }
        } catch (Exception $e) {
            // Log the error
            error_log("Error checking admin status: " . $e->getMessage());
        }
    }

    if (! $isAdmin) {
        // Redirect to home page if not an admin
        header('Location: ../index.php');
        exit;
    }

    // Set page title and active page
    $pageTitle  = 'Admin Panel';
    $activePage = 'admin';

    // Process form submissions
    $message     = '';
    $messageType = '';

    // Handle user creation
    if (isset($_POST['action']) && $_POST['action'] === 'create_user') {
        $name     = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_SPECIAL_CHARS);
        $username = filter_input(INPUT_POST, 'username', FILTER_SANITIZE_SPECIAL_CHARS);
        $email    = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
        $password = $_POST['password']; // No sanitization for password
        $role     = filter_input(INPUT_POST, 'role', FILTER_SANITIZE_SPECIAL_CHARS);

        // Validate input
        if (empty($name) || empty($username) || empty($email) || empty($password) || empty($role)) {
            $message     = 'All fields are required.';
            $messageType = 'error';
        } elseif (! filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $message     = 'Please enter a valid email address.';
            $messageType = 'error';
        } elseif (strlen($username) < 3) {
            $message     = 'Username must be at least 3 characters long.';
            $messageType = 'error';
        } elseif (strlen($password) < 6) {
            $message     = 'Password must be at least 6 characters long.';
            $messageType = 'error';
        } elseif (! in_array($role, ['user', 'subscriber', 'admin'])) {
            $message     = 'Invalid role selected.';
            $messageType = 'error';
        } else {
            // Check if username or email already exists
            $checkSql  = "SELECT id FROM Users WHERE username = ? OR email = ?";
            $checkStmt = $connection->prepare($checkSql);
            $checkStmt->bind_param("ss", $username, $email);
            $checkStmt->execute();
            $checkResult = $checkStmt->get_result();

            if ($checkResult && $checkResult->num_rows > 0) {
                $message     = 'Username or email already exists.';
                $messageType = 'error';
            } else {
                // Check if role column exists
                $checkRoleColumnSql    = "SHOW COLUMNS FROM Users LIKE 'role'";
                $checkRoleColumnResult = $connection->query($checkRoleColumnSql);

                if ($checkRoleColumnResult && $checkRoleColumnResult->num_rows > 0) {
                    // Role column exists, include it in the insert
                    $sql  = "INSERT INTO Users (name, username, email, password, role) VALUES (?, ?, ?, ?, ?)";
                    $stmt = $connection->prepare($sql);

                    // Hash the password
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

                    $stmt->bind_param("sssss", $name, $username, $email, $hashedPassword, $role);
                } else {
                    // Role column doesn't exist, insert without it
                    $sql  = "INSERT INTO Users (name, username, email, password) VALUES (?, ?, ?, ?)";
                    $stmt = $connection->prepare($sql);

                    // Hash the password
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

                    $stmt->bind_param("ssss", $name, $username, $email, $hashedPassword);
                }

                if ($stmt->execute()) {
                    $message     = 'User created successfully.';
                    $messageType = 'success';
                } else {
                    $message     = 'Error creating user: ' . $stmt->error;
                    $messageType = 'error';
                }
            }
        }
    }

    // Handle user deletion
    if (isset($_POST['action']) && $_POST['action'] === 'delete_user') {
        $userId = filter_input(INPUT_POST, 'user_id', FILTER_SANITIZE_NUMBER_INT);

        // Don't allow deleting your own account
        if ($userId == $_SESSION['user_id']) {
            $message     = 'You cannot delete your own account.';
            $messageType = 'error';
        } else {
            // Delete user
            $sql  = "DELETE FROM Users WHERE id = ?";
            $stmt = $connection->prepare($sql);
            $stmt->bind_param("i", $userId);

            if ($stmt->execute()) {
                $message     = 'User deleted successfully.';
                $messageType = 'success';
            } else {
                $message     = 'Error deleting user: ' . $stmt->error;
                $messageType = 'error';
            }
        }
    }

    // Handle user role update
    if (isset($_POST['action']) && $_POST['action'] === 'update_role') {
        $userId = filter_input(INPUT_POST, 'user_id', FILTER_SANITIZE_NUMBER_INT);
        $role   = filter_input(INPUT_POST, 'role', FILTER_SANITIZE_SPECIAL_CHARS);

        // Validate role
        if (! in_array($role, ['user', 'subscriber', 'admin'])) {
            $message     = 'Invalid role selected.';
            $messageType = 'error';
        } else {
            // Check if role column exists
            $checkRoleColumnSql    = "SHOW COLUMNS FROM Users LIKE 'role'";
            $checkRoleColumnResult = $connection->query($checkRoleColumnSql);

            if ($checkRoleColumnResult && $checkRoleColumnResult->num_rows > 0) {
                // Role column exists, update it
                $sql  = "UPDATE Users SET role = ? WHERE id = ?";
                $stmt = $connection->prepare($sql);
                $stmt->bind_param("si", $role, $userId);

                if ($stmt->execute()) {
                    $message     = 'User role updated successfully.';
                    $messageType = 'success';
                } else {
                    $message     = 'Error updating user role: ' . $stmt->error;
                    $messageType = 'error';
                }
            } else {
                // Role column doesn't exist
                $message     = 'Role column does not exist in the database. Please contact the administrator.';
                $messageType = 'error';
            }
        }
    }

    // Get all users with explicit role selection
    $sql = "SELECT id, name, username, email,
        IFNULL(role, 'user') as role,
        created_at
        FROM Users
        ORDER BY id";

    $result = $connection->query($sql);
    $users  = [];

    if ($result) {
        while ($row = $result->fetch_assoc()) {
            // Debug: Log the role value
            error_log("User ID: " . $row['id'] . ", Username: " . $row['username'] . ", Role: " . $row['role']);
            $users[] = $row;
        }
    }

    // Additional CSS for admin panel
    $additionalHeadContent = <<<EOT
<style>
    .admin-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .admin-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .admin-header h1 {
        margin: 0;
        color: #6b8e23;
    }

    .admin-tabs {
        display: flex;
        margin-bottom: 20px;
        border-bottom: 1px solid #ddd;
    }

    .admin-tab {
        padding: 10px 20px;
        cursor: pointer;
        border: 1px solid transparent;
        border-bottom: none;
        margin-right: 5px;
        border-radius: 5px 5px 0 0;
        background-color: #f9f9f9;
    }

    .admin-tab.active {
        background-color: #fff;
        border-color: #ddd;
        border-bottom-color: #fff;
        font-weight: bold;
        color: #6b8e23;
    }

    .tab-content {
        display: none;
    }

    .tab-content.active {
        display: block;
    }

    .user-table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    .user-table th, .user-table td {
        padding: 10px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }

    .user-table th {
        background-color: #f5f5f5;
        font-weight: bold;
    }

    .user-table tr:hover {
        background-color: #f9f9f9;
    }

    .user-actions {
        display: flex;
        gap: 10px;
    }

    .user-actions button, .user-actions select {
        padding: 5px 10px;
        border: 1px solid #ddd;
        border-radius: 3px;
        background-color: #fff;
        cursor: pointer;
    }

    .user-actions button.delete-btn {
        color: #e74c3c;
    }

    .user-actions button.delete-btn:hover {
        background-color: #ffebee;
    }

    .create-user-form {
        background-color: #fff;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }

    .form-group input, .form-group select {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
    }

    .submit-btn {
        background-color: #b2d8b2;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        font-weight: bold;
    }

    .submit-btn:hover {
        background-color: #98c298;
    }

    .message {
        padding: 10px;
        margin-bottom: 20px;
        border-radius: 5px;
    }

    .message.success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .message.error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .role-badge {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: bold;
    }

    .role-badge.admin {
        background-color: #ffd700;
        color: #333;
    }

    .role-badge.subscriber {
        background-color: #b2d8b2;
        color: #333;
    }

    .role-badge.user {
        background-color: #e0e0e0;
        color: #333;
    }
</style>
EOT;

    // Include header
    include __DIR__ . '/../includes/header.php';
?>

<main>
    <section class="page-header">
        <h1><i class="fas fa-users-cog"></i> Admin Panel</h1>
        <p>Manage users and system settings</p>
    </section>

    <section class="admin-container">
        <?php if (! empty($message)): ?>
            <div class="message<?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <div class="admin-tabs">
            <div class="admin-tab active" data-tab="users">User Management</div>
            <div class="admin-tab" data-tab="settings">System Settings</div>
        </div>

        <div class="tab-content active" id="users-tab">
            <div class="admin-header">
                <h2>User Management</h2>
                <button id="show-create-form-btn" class="submit-btn"><i class="fas fa-user-plus"></i> Create New User</button>
            </div>

            <div id="create-user-form" class="create-user-form" style="display: none; margin-bottom: 20px;">
                <h3>Create New User</h3>
                <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                    <input type="hidden" name="action" value="create_user">

                    <div class="form-group">
                        <label for="name">Name:</label>
                        <input type="text" id="name" name="name" required>
                    </div>

                    <div class="form-group">
                        <label for="username">Username:</label>
                        <input type="text" id="username" name="username" required minlength="3">
                    </div>

                    <div class="form-group">
                        <label for="email">Email:</label>
                        <input type="email" id="email" name="email" required>
                    </div>

                    <div class="form-group">
                        <label for="password">Password:</label>
                        <input type="password" id="password" name="password" required minlength="6">
                    </div>

                    <div class="form-group">
                        <label for="role">Role:</label>
                        <select id="role" name="role" required>
                            <option value="user">Regular User</option>
                            <option value="subscriber">Subscriber</option>
                            <option value="admin">Administrator</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="submit-btn">Create User</button>
                        <button type="button" id="cancel-create-btn" style="background-color: #f8f9fa; color: #333; border: 1px solid #ddd; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-left: 10px;">Cancel</button>
                    </div>
                </form>
            </div>

            <table class="user-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Username</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($users)): ?>
                        <tr>
                            <td colspan="7" style="text-align: center;">No users found.</td>
                        </tr>
                    <?php else: ?>
<?php foreach ($users as $user): ?>
                            <tr>
                                <td><?php echo $user['id']; ?></td>
                                <td><?php echo htmlspecialchars($user['name']); ?></td>
                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                <td>
                                    <?php
                                        // Debug output to see what's in the role field
                                        echo "<!-- Role value: " . htmlspecialchars($user['role']) . " -->";
                                        $roleClass = htmlspecialchars($user['role']);
                                        $roleName  = ucfirst(htmlspecialchars($user['role']));
                                    ?>
                                    <span class="role-badge<?php echo $roleClass; ?>">
                                        <?php echo $roleName; ?>
                                    </span>
                                </td>
                                <td><?php echo date('M d, Y', strtotime($user['created_at'])); ?></td>
                                <td>
                                    <div class="user-actions">
                                        <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" style="display: inline;">
                                            <input type="hidden" name="action" value="update_role">
                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                            <select name="role" onchange="this.form.submit()">
                                                <option value="user"                                                                     <?php echo $user['role'] === 'user' ? 'selected' : ''; ?>>User</option>
                                                <option value="subscriber"                                                                           <?php echo $user['role'] === 'subscriber' ? 'selected' : ''; ?>>Subscriber</option>
                                                <option value="admin"                                                                      <?php echo $user['role'] === 'admin' ? 'selected' : ''; ?>>Admin</option>
                                            </select>
                                        </form>

                                        <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                            <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this user?');">
                                                <input type="hidden" name="action" value="delete_user">
                                                <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                <button type="submit" class="delete-btn"><i class="fas fa-trash"></i> Delete</button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
<?php endif; ?>
                </tbody>
            </table>
        </div>

        <div class="tab-content" id="settings-tab">
            <h2>System Settings</h2>
            <p>This section is under development.</p>
        </div>
    </section>
</main>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tab switching
        const tabs = document.querySelectorAll('.admin-tab');
        const tabContents = document.querySelectorAll('.tab-content');

        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs and contents
                tabs.forEach(t => t.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));

                // Add active class to clicked tab
                this.classList.add('active');

                // Show corresponding content
                const tabId = this.getAttribute('data-tab');
                document.getElementById(tabId + '-tab').classList.add('active');
            });
        });

        // Show/hide create user form
        const showCreateFormBtn = document.getElementById('show-create-form-btn');
        const createUserForm = document.getElementById('create-user-form');
        const cancelCreateBtn = document.getElementById('cancel-create-btn');

        if (showCreateFormBtn && createUserForm && cancelCreateBtn) {
            showCreateFormBtn.addEventListener('click', function() {
                createUserForm.style.display = 'block';
                showCreateFormBtn.style.display = 'none';
            });

            cancelCreateBtn.addEventListener('click', function() {
                createUserForm.style.display = 'none';
                showCreateFormBtn.style.display = 'block';
            });
        }
    });
</script>

<?php include __DIR__ . '/../includes/footer.php'; ?>
