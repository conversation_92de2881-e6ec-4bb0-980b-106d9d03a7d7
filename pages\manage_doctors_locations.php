<?php
    // Start session
    session_start();

    // Include the appointment functions
    require_once '../includes/appointment_functions_updated.php';

    // Set a default user ID for demonstration
    $userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 4; // This would normally come from the logged-in user's session

    // Initialize variables
    $message      = '';
    $messageType  = '';
    $editDoctor   = null;
    $editLocation = null;

    // Get all doctors and locations
    $doctors   = getAllDoctors();
    $locations = getAllLocations();

    // Process form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (isset($_POST['action'])) {
            // Add new doctor
            if ($_POST['action'] === 'add_doctor') {
                $name      = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_SPECIAL_CHARS);
                $specialty = filter_input(INPUT_POST, 'specialty', FILTER_SANITIZE_SPECIAL_CHARS);
                $phone     = filter_input(INPUT_POST, 'phone', FILTER_SANITIZE_SPECIAL_CHARS);
                $email     = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
                $notes     = filter_input(INPUT_POST, 'notes', FILTER_SANITIZE_SPECIAL_CHARS);

                if ($name) {
                    $result = createDoctor($name, $specialty, $phone, $email, $notes);

                    if ($result) {
                        $message     = "Doctor added successfully!";
                        $messageType = "success";

                        // Refresh doctors list
                        $doctors = getAllDoctors();
                    } else {
                        $message     = "Error adding doctor.";
                        $messageType = "error";
                    }
                } else {
                    $message     = "Please enter a doctor name.";
                    $messageType = "error";
                }
            }

            // Edit doctor
            if ($_POST['action'] === 'edit_doctor' && isset($_POST['doctor_id'])) {
                $doctorId  = filter_input(INPUT_POST, 'doctor_id', FILTER_VALIDATE_INT);
                $name      = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_SPECIAL_CHARS);
                $specialty = filter_input(INPUT_POST, 'specialty', FILTER_SANITIZE_SPECIAL_CHARS);
                $phone     = filter_input(INPUT_POST, 'phone', FILTER_SANITIZE_SPECIAL_CHARS);
                $email     = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
                $notes     = filter_input(INPUT_POST, 'notes', FILTER_SANITIZE_SPECIAL_CHARS);

                if ($doctorId && $name) {
                    $result = updateDoctor($doctorId, $name, $specialty, $phone, $email, $notes);

                    if ($result) {
                        $message     = "Doctor updated successfully!";
                        $messageType = "success";

                        // Refresh doctors list
                        $doctors = getAllDoctors();
                    } else {
                        $message     = "Error updating doctor.";
                        $messageType = "error";
                    }
                } else {
                    $message     = "Please enter a doctor name.";
                    $messageType = "error";
                }
            }

            // Delete doctor
            if ($_POST['action'] === 'delete_doctor' && isset($_POST['doctor_id'])) {
                $doctorId = filter_input(INPUT_POST, 'doctor_id', FILTER_VALIDATE_INT);

                if ($doctorId && deleteDoctor($doctorId)) {
                    $message     = "Doctor deleted successfully!";
                    $messageType = "success";

                    // Refresh doctors list
                    $doctors = getAllDoctors();
                } else {
                    $message     = "Error deleting doctor. The doctor may be referenced in appointments.";
                    $messageType = "error";
                }
            }

            // Add new location
            if ($_POST['action'] === 'add_location') {
                $name       = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_SPECIAL_CHARS);
                $address    = filter_input(INPUT_POST, 'address', FILTER_SANITIZE_SPECIAL_CHARS);
                $city       = filter_input(INPUT_POST, 'city', FILTER_SANITIZE_SPECIAL_CHARS);
                $postalCode = filter_input(INPUT_POST, 'postal_code', FILTER_SANITIZE_SPECIAL_CHARS);
                $phone      = filter_input(INPUT_POST, 'phone', FILTER_SANITIZE_SPECIAL_CHARS);
                $notes      = filter_input(INPUT_POST, 'notes', FILTER_SANITIZE_SPECIAL_CHARS);

                if ($name) {
                    $result = createLocation($name, $address, $city, $postalCode, $phone, $notes);

                    if ($result) {
                        $message     = "Location added successfully!";
                        $messageType = "success";

                        // Refresh locations list
                        $locations = getAllLocations();
                    } else {
                        $message     = "Error adding location.";
                        $messageType = "error";
                    }
                } else {
                    $message     = "Please enter a location name.";
                    $messageType = "error";
                }
            }

            // Edit location
            if ($_POST['action'] === 'edit_location' && isset($_POST['location_id'])) {
                $locationId = filter_input(INPUT_POST, 'location_id', FILTER_VALIDATE_INT);
                $name       = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_SPECIAL_CHARS);
                $address    = filter_input(INPUT_POST, 'address', FILTER_SANITIZE_SPECIAL_CHARS);
                $city       = filter_input(INPUT_POST, 'city', FILTER_SANITIZE_SPECIAL_CHARS);
                $postalCode = filter_input(INPUT_POST, 'postal_code', FILTER_SANITIZE_SPECIAL_CHARS);
                $phone      = filter_input(INPUT_POST, 'phone', FILTER_SANITIZE_SPECIAL_CHARS);
                $notes      = filter_input(INPUT_POST, 'notes', FILTER_SANITIZE_SPECIAL_CHARS);

                if ($locationId && $name) {
                    $result = updateLocation($locationId, $name, $address, $city, $postalCode, $phone, $notes);

                    if ($result) {
                        $message     = "Location updated successfully!";
                        $messageType = "success";

                        // Refresh locations list
                        $locations = getAllLocations();
                    } else {
                        $message     = "Error updating location.";
                        $messageType = "error";
                    }
                } else {
                    $message     = "Please enter a location name.";
                    $messageType = "error";
                }
            }

            // Delete location
            if ($_POST['action'] === 'delete_location' && isset($_POST['location_id'])) {
                $locationId = filter_input(INPUT_POST, 'location_id', FILTER_VALIDATE_INT);

                if ($locationId && deleteLocation($locationId)) {
                    $message     = "Location deleted successfully!";
                    $messageType = "success";

                    // Refresh locations list
                    $locations = getAllLocations();
                } else {
                    $message     = "Error deleting location. The location may be referenced in appointments.";
                    $messageType = "error";
                }
            }
        }
    }

    // Check if we're editing a doctor
    if (isset($_GET['action']) && $_GET['action'] === 'edit_doctor' && isset($_GET['id'])) {
        $doctorId = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
        if ($doctorId) {
            $editDoctor = getDoctorById($doctorId);
        }
    }

    // Check if we're editing a location
    if (isset($_GET['action']) && $_GET['action'] === 'edit_location' && isset($_GET['id'])) {
        $locationId = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
        if ($locationId) {
            $editLocation = getLocationById($locationId);
        }
    }
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Doctors & Locations - PregnancyCare</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }

        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
        }

        .tab.active {
            background-color: #f9f9f9;
            border-color: #ddd;
            border-bottom-color: #f9f9f9;
            margin-bottom: -1px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .card {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .card h3 {
            margin-top: 0;
            color: #6b8e23;
        }

        .card-actions {
            margin-top: 15px;
            text-align: right;
        }

        .card-actions a,
        .card-actions button {
            display: inline-block;
            margin-left: 10px;
        }

        .message {
            padding: 10px 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .message.success {
            background-color: #e8f5e9;
            color: #2e7d32;
            border-left: 4px solid #2e7d32;
        }

        .message.error {
            background-color: #ffebee;
            color: #c62828;
            border-left: 4px solid #c62828;
        }
    </style>
</head>

<body>
    <div class="wrapper">
        <nav>
            <div class="nav-container">
                <div class="logo-container">
                    <a href="../index.php" class="logo">PregnancyCare</a>
                </div>
                <button class="hamburger" aria-label="Menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
                <div class="nav-links">
                    <a href="../index.php#pregnancy">Pregnancy</a>
                    <a href="../index.php#community">Community</a>
                    <a href="../index.php#agenda">Agenda</a>
                    <a href="../index.php#health">Health</a>
                    <a href="../index.php#family">Family</a>
                    <a href="baby_updated.php">Baby</a>
                    <a href="appointments.php">Appointments</a>
                </div>
                <div class="login-container">
                    <a href="#login" class="login-btn">Login</a>
                </div>
            </div>
        </nav>

        <main>
            <section class="page-header">
                <h1><i class="fas fa-user-md"></i> Manage Doctors & Locations</h1>
                <p>Add, edit, and manage your healthcare providers and appointment locations.</p>
            </section>

            <?php if (! empty($message)): ?>
                <div class="message<?php echo $messageType; ?>">
                    <?php echo $message; ?>
                </div>
            <?php endif; ?>

            <section class="content-section">
                <div class="content-wrapper">
                    <div class="tabs">
                        <div class="tab active" data-tab="doctors">Doctors</div>
                        <div class="tab" data-tab="locations">Locations</div>
                    </div>

                    <!-- Doctors Tab -->
                    <div class="tab-content active" id="doctors-tab">
                        <div class="content-box">
                            <h2><?php echo $editDoctor ? 'Edit Doctor' : 'Add New Doctor'; ?></h2>
                            <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                                <input type="hidden" name="action" value="<?php echo $editDoctor ? 'edit_doctor' : 'add_doctor'; ?>">
                                <?php if ($editDoctor): ?>
                                    <input type="hidden" name="doctor_id" value="<?php echo $editDoctor['id']; ?>">
                                <?php endif; ?>

                                <div class="form-group">
                                    <label for="doctor-name">Name:</label>
                                    <input type="text" id="doctor-name" name="name" required
                                        value="<?php echo $editDoctor ? htmlspecialchars($editDoctor['name']) : ''; ?>">
                                </div>

                                <div class="form-group">
                                    <label for="doctor-specialty">Specialty:</label>
                                    <input type="text" id="doctor-specialty" name="specialty"
                                        value="<?php echo $editDoctor ? htmlspecialchars($editDoctor['specialty']) : ''; ?>">
                                </div>

                                <div class="form-group">
                                    <label for="doctor-phone">Phone:</label>
                                    <input type="text" id="doctor-phone" name="phone"
                                        value="<?php echo $editDoctor ? htmlspecialchars($editDoctor['phone']) : ''; ?>">
                                </div>

                                <div class="form-group">
                                    <label for="doctor-email">Email:</label>
                                    <input type="email" id="doctor-email" name="email"
                                        value="<?php echo $editDoctor ? htmlspecialchars($editDoctor['email']) : ''; ?>">
                                </div>

                                <div class="form-group">
                                    <label for="doctor-notes">Notes:</label>
                                    <textarea id="doctor-notes" name="notes" rows="3"><?php echo $editDoctor ? htmlspecialchars($editDoctor['notes']) : ''; ?></textarea>
                                </div>

                                <div class="form-group">
                                    <button type="submit" class="submit-btn">
                                        <?php echo $editDoctor ? 'Update Doctor' : 'Add Doctor'; ?>
                                    </button>
                                    <?php if ($editDoctor): ?>
                                        <a href="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" class="cancel-btn">Cancel</a>
                                    <?php endif; ?>
                                </div>
                            </form>
                        </div>

                        <div class="content-box">
                            <h2>Your Doctors</h2>

                            <?php if (empty($doctors)): ?>
                                <p>You haven't added any doctors yet.</p>
                            <?php else: ?>
<?php foreach ($doctors as $doctor): ?>
                                    <div class="card">
                                        <h3><?php echo htmlspecialchars($doctor['name']); ?></h3>

                                        <?php if (! empty($doctor['specialty'])): ?>
                                            <p><strong>Specialty:</strong><?php echo htmlspecialchars($doctor['specialty']); ?></p>
                                        <?php endif; ?>

                                        <?php if (! empty($doctor['phone'])): ?>
                                            <p><strong>Phone:</strong><?php echo htmlspecialchars($doctor['phone']); ?></p>
                                        <?php endif; ?>

                                        <?php if (! empty($doctor['email'])): ?>
                                            <p><strong>Email:</strong><?php echo htmlspecialchars($doctor['email']); ?></p>
                                        <?php endif; ?>

                                        <?php if (! empty($doctor['notes'])): ?>
                                            <p><strong>Notes:</strong><?php echo htmlspecialchars($doctor['notes']); ?></p>
                                        <?php endif; ?>

                                        <div class="card-actions">
                                            <a href="?action=edit_doctor&id=<?php echo $doctor['id']; ?>" class="edit-btn">Edit</a>

                                            <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>"
                                                onsubmit="return confirm('Are you sure you want to delete this doctor?');"
                                                style="display: inline;">
                                                <input type="hidden" name="action" value="delete_doctor">
                                                <input type="hidden" name="doctor_id" value="<?php echo $doctor['id']; ?>">
                                                <button type="submit" class="delete-btn">Delete</button>
                                            </form>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
<?php endif; ?>
                        </div>
                    </div>

                    <!-- Locations Tab -->
                    <div class="tab-content" id="locations-tab">
                        <div class="content-box">
                            <h2><?php echo $editLocation ? 'Edit Location' : 'Add New Location'; ?></h2>
                            <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>">
                                <input type="hidden" name="action" value="<?php echo $editLocation ? 'edit_location' : 'add_location'; ?>">
                                <?php if ($editLocation): ?>
                                    <input type="hidden" name="location_id" value="<?php echo $editLocation['id']; ?>">
                                <?php endif; ?>

                                <div class="form-group">
                                    <label for="location-name">Name:</label>
                                    <input type="text" id="location-name" name="name" required
                                        value="<?php echo $editLocation ? htmlspecialchars($editLocation['name']) : ''; ?>">
                                </div>

                                <div class="form-group">
                                    <label for="location-address">Address:</label>
                                    <input type="text" id="location-address" name="address"
                                        value="<?php echo $editLocation ? htmlspecialchars($editLocation['address']) : ''; ?>">
                                </div>

                                <div class="form-group">
                                    <label for="location-city">City:</label>
                                    <input type="text" id="location-city" name="city"
                                        value="<?php echo $editLocation ? htmlspecialchars($editLocation['city']) : ''; ?>">
                                </div>

                                <div class="form-group">
                                    <label for="location-postal-code">Postal Code:</label>
                                    <input type="text" id="location-postal-code" name="postal_code"
                                        value="<?php echo $editLocation ? htmlspecialchars($editLocation['postal_code']) : ''; ?>">
                                </div>

                                <div class="form-group">
                                    <label for="location-phone">Phone:</label>
                                    <input type="text" id="location-phone" name="phone"
                                        value="<?php echo $editLocation ? htmlspecialchars($editLocation['phone']) : ''; ?>">
                                </div>

                                <div class="form-group">
                                    <label for="location-notes">Notes:</label>
                                    <textarea id="location-notes" name="notes" rows="3"><?php echo $editLocation ? htmlspecialchars($editLocation['notes']) : ''; ?></textarea>
                                </div>

                                <div class="form-group">
                                    <button type="submit" class="submit-btn">
                                        <?php echo $editLocation ? 'Update Location' : 'Add Location'; ?>
                                    </button>
                                    <?php if ($editLocation): ?>
                                        <a href="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>" class="cancel-btn">Cancel</a>
                                    <?php endif; ?>
                                </div>
                            </form>
                        </div>

                        <div class="content-box">
                            <h2>Your Locations</h2>

                            <?php if (empty($locations)): ?>
                                <p>You haven't added any locations yet.</p>
                            <?php else: ?>
<?php foreach ($locations as $location): ?>
                                    <div class="card">
                                        <h3><?php echo htmlspecialchars($location['name']); ?></h3>

                                        <?php if (! empty($location['address'])): ?>
                                            <p><strong>Address:</strong><?php echo htmlspecialchars($location['address']); ?></p>
                                        <?php endif; ?>

                                        <?php if (! empty($location['city'])): ?>
                                            <p><strong>City:</strong><?php echo htmlspecialchars($location['city']); ?></p>
                                        <?php endif; ?>

                                        <?php if (! empty($location['postal_code'])): ?>
                                            <p><strong>Postal Code:</strong><?php echo htmlspecialchars($location['postal_code']); ?></p>
                                        <?php endif; ?>

                                        <?php if (! empty($location['phone'])): ?>
                                            <p><strong>Phone:</strong><?php echo htmlspecialchars($location['phone']); ?></p>
                                        <?php endif; ?>

                                        <?php if (! empty($location['notes'])): ?>
                                            <p><strong>Notes:</strong><?php echo htmlspecialchars($location['notes']); ?></p>
                                        <?php endif; ?>

                                        <div class="card-actions">
                                            <a href="?action=edit_location&id=<?php echo $location['id']; ?>" class="edit-btn">Edit</a>

                                            <form method="post" action="<?php echo htmlspecialchars($_SERVER['PHP_SELF']); ?>"
                                                onsubmit="return confirm('Are you sure you want to delete this location?');"
                                                style="display: inline;">
                                                <input type="hidden" name="action" value="delete_location">
                                                <input type="hidden" name="location_id" value="<?php echo $location['id']; ?>">
                                                <button type="submit" class="delete-btn">Delete</button>
                                            </form>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
<?php endif; ?>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <?php include __DIR__ . '/../includes/footer.php'; ?>
    </div>

    <script src="../assets/js/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab functionality
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // Remove active class from all tabs and contents
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));

                    // Add active class to clicked tab and corresponding content
                    this.classList.add('active');
                    document.getElementById(this.dataset.tab + '-tab').classList.add('active');
                });
            });

            // If there's an edit parameter in the URL, activate the appropriate tab
            const urlParams = new URLSearchParams(window.location.search);
            const action = urlParams.get('action');

            if (action) {
                if (action.includes('location')) {
                    document.querySelector('.tab[data-tab="locations"]').click();
                } else if (action.includes('doctor')) {
                    document.querySelector('.tab[data-tab="doctors"]').click();
                }
            }
        });
    </script>
</body>

</html>