<?php

/**
 * Functions for handling appointments and related entities
 */

// Include database connection
require_once 'database.php';

/**
 * Get all doctors
 *
 * @return array Array of doctors
 */
function getAllDoctors()
{
    global $connection;

    $sql    = "SELECT id, name, specialty, phone, email, notes FROM Doctors ORDER BY name";
    $result = $connection->query($sql);

    $doctors = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $doctors[] = $row;
        }
    }

    return $doctors;
}

/**
 * Get doctor by ID
 *
 * @param int $doctorId Doctor ID
 * @return array|null Doctor data or null if not found
 */
function getDoctorById($doctorId)
{
    global $connection;

    $sql  = "SELECT id, name, specialty, phone, email, notes FROM Doctors WHERE id = ?";
    $stmt = $connection->prepare($sql);
    $stmt->bind_param("i", $doctorId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return null;
}

/**
 * Create a new doctor
 *
 * @param string $name Doctor name
 * @param string $specialty Doctor specialty
 * @param string $phone Doctor phone
 * @param string $email Doctor email
 * @param string $notes Additional notes
 * @return int|false New doctor ID or false on failure
 */
function createDoctor($name, $specialty = '', $phone = '', $email = '', $notes = '')
{
    global $connection;

    $sql  = "INSERT INTO Doctors (name, specialty, phone, email, notes) VALUES (?, ?, ?, ?, ?)";
    $stmt = $connection->prepare($sql);
    $stmt->bind_param("sssss", $name, $specialty, $phone, $email, $notes);

    if ($stmt->execute()) {
        return $connection->insert_id;
    }

    return false;
}

/**
 * Update doctor information
 *
 * @param int $doctorId Doctor ID
 * @param string $name Doctor name
 * @param string $specialty Doctor specialty
 * @param string $phone Doctor phone
 * @param string $email Doctor email
 * @param string $notes Additional notes
 * @return bool True if successful, false otherwise
 */
function updateDoctor($doctorId, $name, $specialty = '', $phone = '', $email = '', $notes = '')
{
    global $connection;

    $sql  = "UPDATE Doctors SET name = ?, specialty = ?, phone = ?, email = ?, notes = ? WHERE id = ?";
    $stmt = $connection->prepare($sql);
    $stmt->bind_param("sssssi", $name, $specialty, $phone, $email, $notes, $doctorId);

    return $stmt->execute();
}

/**
 * Delete a doctor
 *
 * @param int $doctorId Doctor ID
 * @return bool True if successful, false otherwise
 */
function deleteDoctor($doctorId)
{
    global $connection;

    $sql  = "DELETE FROM Doctors WHERE id = ?";
    $stmt = $connection->prepare($sql);
    $stmt->bind_param("i", $doctorId);

    return $stmt->execute();
}

/**
 * Get all locations
 *
 * @return array Array of locations
 */
function getAllLocations()
{
    global $connection;

    $sql    = "SELECT id, name, address, city, postal_code, phone, notes FROM Locations ORDER BY name";
    $result = $connection->query($sql);

    $locations = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $locations[] = $row;
        }
    }

    return $locations;
}

/**
 * Get location by ID
 *
 * @param int $locationId Location ID
 * @return array|null Location data or null if not found
 */
function getLocationById($locationId)
{
    global $connection;

    $sql  = "SELECT id, name, address, city, postal_code, phone, notes FROM Locations WHERE id = ?";
    $stmt = $connection->prepare($sql);
    $stmt->bind_param("i", $locationId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return null;
}

/**
 * Create a new location
 *
 * @param string $name Location name
 * @param string $address Location address
 * @param string $city Location city
 * @param string $postalCode Location postal code
 * @param string $phone Location phone
 * @param string $notes Additional notes
 * @return int|false New location ID or false on failure
 */
function createLocation($name, $address = '', $city = '', $postalCode = '', $phone = '', $notes = '')
{
    global $connection;

    $sql  = "INSERT INTO Locations (name, address, city, postal_code, phone, notes) VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $connection->prepare($sql);
    $stmt->bind_param("ssssss", $name, $address, $city, $postalCode, $phone, $notes);

    if ($stmt->execute()) {
        return $connection->insert_id;
    }

    return false;
}

/**
 * Update location information
 *
 * @param int $locationId Location ID
 * @param string $name Location name
 * @param string $address Location address
 * @param string $city Location city
 * @param string $postalCode Location postal code
 * @param string $phone Location phone
 * @param string $notes Additional notes
 * @return bool True if successful, false otherwise
 */
function updateLocation($locationId, $name, $address = '', $city = '', $postalCode = '', $phone = '', $notes = '')
{
    global $connection;

    $sql  = "UPDATE Locations SET name = ?, address = ?, city = ?, postal_code = ?, phone = ?, notes = ? WHERE id = ?";
    $stmt = $connection->prepare($sql);
    $stmt->bind_param("ssssssi", $name, $address, $city, $postalCode, $phone, $notes, $locationId);

    return $stmt->execute();
}

/**
 * Delete a location
 *
 * @param int $locationId Location ID
 * @return bool True if successful, false otherwise
 */
function deleteLocation($locationId)
{
    global $connection;

    $sql  = "DELETE FROM Locations WHERE id = ?";
    $stmt = $connection->prepare($sql);
    $stmt->bind_param("i", $locationId);

    return $stmt->execute();
}

/**
 * Get user's appointments
 *
 * @param int $userId User ID
 * @return array Array of appointments
 */
function getUserAppointments($userId)
{
    global $connection;

    $sql = "SELECT a.id, a.date, a.time, a.notes, a.agenda_id,
                   d.id as doctor_id, d.name as doctor_name, d.specialty as doctor_specialty,
                   l.id as location_id, l.name as location_name, l.address as location_address
            FROM Appointments a
            LEFT JOIN Doctors d ON a.doctor_id = d.id
            LEFT JOIN Locations l ON a.location_id = l.id
            WHERE a.user_id = ?
            ORDER BY a.date DESC, a.time DESC";

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    $appointments = [];
    while ($row = $result->fetch_assoc()) {
        $appointments[] = $row;
    }

    return $appointments;
}

/**
 * Get appointment by ID
 *
 * @param int $appointmentId Appointment ID
 * @return array|null Appointment data or null if not found
 */
function getAppointmentById($appointmentId)
{
    global $connection;

    $sql = "SELECT a.id, a.user_id, a.doctor_id, a.location_id, a.date, a.time, a.notes, a.agenda_id,
                   d.name as doctor_name, l.name as location_name
            FROM Appointments a
            LEFT JOIN Doctors d ON a.doctor_id = d.id
            LEFT JOIN Locations l ON a.location_id = l.id
            WHERE a.id = ?";

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("i", $appointmentId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result && $result->num_rows > 0) {
        return $result->fetch_assoc();
    }

    return null;
}

/**
 * Create a new appointment
 *
 * @param int $userId User ID
 * @param int $doctorId Doctor ID
 * @param int $locationId Location ID
 * @param string $date Appointment date
 * @param string $time Appointment time
 * @param string $notes Additional notes
 * @param int|null $agendaId Agenda ID
 * @return int|false New appointment ID or false on failure
 */
function createAppointment($userId, $doctorId, $locationId, $date, $time, $notes = '', $agendaId = null)
{
    global $connection;

    $sql = "INSERT INTO Appointments (user_id, doctor_id, location_id, date, time, notes, agenda_id)
            VALUES (?, ?, ?, ?, ?, ?, ?)";

    $stmt = $connection->prepare($sql);
    $stmt->bind_param("iiisssi", $userId, $doctorId, $locationId, $date, $time, $notes, $agendaId);

    if ($stmt->execute()) {
        return $connection->insert_id;
    }

    return false;
}

/**
 * Update appointment information and its associated agenda item
 *
 * @param int $appointmentId Appointment ID
 * @param int $doctorId Doctor ID
 * @param int $locationId Location ID
 * @param string $date Appointment date
 * @param string $time Appointment time
 * @param string $notes Additional notes
 * @return bool True if successful, false otherwise
 */
function updateAppointment($appointmentId, $doctorId, $locationId, $date, $time, $notes = '')
{
    global $connection;

    // Start transaction
    $connection->begin_transaction();

    try {
        // Get the current appointment to find its agenda_id
        $appointment = getAppointmentById($appointmentId);
        if (! $appointment) {
            throw new Exception("Appointment not found");
        }

        $agendaId = $appointment['agenda_id'];

        // Update the appointment
        $sql = "UPDATE Appointments
                SET doctor_id = ?, location_id = ?, date = ?, time = ?, notes = ?
                WHERE id = ?";

        $stmt = $connection->prepare($sql);
        $stmt->bind_param("iisssi", $doctorId, $locationId, $date, $time, $notes, $appointmentId);
        $stmt->execute();

        // If there's an associated agenda item, update it too
        if ($agendaId) {
            // Create description for agenda
            $description = "Medical Appointment";
            if ($doctorId) {
                $doctor = getDoctorById($doctorId);
                if ($doctor) {
                    $description .= " with " . $doctor['name'];
                    if (! empty($doctor['specialty'])) {
                        $description .= " (" . $doctor['specialty'] . ")";
                    }
                }
            }

            if ($locationId) {
                $location = getLocationById($locationId);
                if ($location) {
                    $description .= " at " . $location['name'];
                }
            }

            // Add notes to description if available
            if (! empty($notes)) {
                $description .= "\n\nNotes: " . $notes;
            }

            // Update the agenda item
            $sql = "UPDATE Agenda
                    SET date = ?, time = ?, description = ?
                    WHERE id = ?";

            $stmt = $connection->prepare($sql);
            $stmt->bind_param("sssi", $date, $time, $description, $agendaId);
            $stmt->execute();
        }

        // Commit transaction
        $connection->commit();
        return true;
    } catch (Exception $e) {
        // Rollback transaction on error
        $connection->rollback();
        return false;
    }
}

/**
 * Delete an appointment and its associated agenda item
 *
 * @param int $appointmentId Appointment ID
 * @return bool True if successful, false otherwise
 */
function deleteAppointment($appointmentId)
{
    global $connection;

    // Start transaction
    $connection->begin_transaction();

    try {
        // Get the agenda_id associated with this appointment
        $sql  = "SELECT agenda_id FROM Appointments WHERE id = ?";
        $stmt = $connection->prepare($sql);
        $stmt->bind_param("i", $appointmentId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result && $result->num_rows > 0) {
            $row      = $result->fetch_assoc();
            $agendaId = $row['agenda_id'];

            // Delete the appointment
            $sql  = "DELETE FROM Appointments WHERE id = ?";
            $stmt = $connection->prepare($sql);
            $stmt->bind_param("i", $appointmentId);
            $stmt->execute();

            // If there's an associated agenda item, delete it too
            if ($agendaId) {
                $sql  = "DELETE FROM Agenda WHERE id = ?";
                $stmt = $connection->prepare($sql);
                $stmt->bind_param("i", $agendaId);
                $stmt->execute();
            }

            // Commit transaction
            $connection->commit();
            return true;
        } else {
            // Appointment not found
            $connection->rollback();
            return false;
        }
    } catch (Exception $e) {
        // Rollback transaction on error
        $connection->rollback();
        return false;
    }
}

/**
 * Create a new appointment and add it to the agenda
 *
 * @param int $userId User ID
 * @param int $doctorId Doctor ID
 * @param int $locationId Location ID
 * @param string $title Appointment title
 * @param string $date Appointment date
 * @param string $time Appointment time
 * @param int $duration Duration in minutes
 * @param string $notes Additional notes
 * @return int|false New appointment ID or false on failure
 */
function createAppointmentWithAgenda($userId, $doctorId, $locationId, $title, $date, $time, $duration = 60, $notes = '')
{
    global $connection;

    // Start transaction
    $connection->begin_transaction();

    try {
        // Create agenda item
        $sql  = "INSERT INTO Agenda (user_id, title, description, date, time) VALUES (?, ?, ?, ?, ?)";
        $stmt = $connection->prepare($sql);

        $description = "Medical Appointment";
        if ($doctorId) {
            $doctor = getDoctorById($doctorId);
            if ($doctor) {
                $description .= " with " . $doctor['name'];
                if (! empty($doctor['specialty'])) {
                    $description .= " (" . $doctor['specialty'] . ")";
                }
            }
        }

        if ($locationId) {
            $location = getLocationById($locationId);
            if ($location) {
                $description .= " at " . $location['name'];
            }
        }

        $stmt->bind_param("issss", $userId, $title, $description, $date, $time);
        $stmt->execute();
        $agendaId = $connection->insert_id;

        // Create appointment
        $sql = "INSERT INTO Appointments (user_id, doctor_id, location_id, date, time, notes, agenda_id)
                VALUES (?, ?, ?, ?, ?, ?, ?)";
        $stmt = $connection->prepare($sql);
        $stmt->bind_param("iiisssi", $userId, $doctorId, $locationId, $date, $time, $notes, $agendaId);
        $stmt->execute();
        $appointmentId = $connection->insert_id;

        // Commit transaction
        $connection->commit();

        return $appointmentId;
    } catch (Exception $e) {
        // Rollback transaction on error
        $connection->rollback();
        return false;
    }
}
